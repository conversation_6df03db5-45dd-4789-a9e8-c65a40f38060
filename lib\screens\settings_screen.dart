import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../config/app_config.dart';
import 'change_password_screen.dart';
import '../services/biometric_service.dart';
import '../services/language_service.dart';

import '../providers/language_provider.dart';
import '../generated/l10n/app_localizations.dart';

/// شاشة الإعدادات
class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _isBiometricEnabled = false;
  bool _isLoadingBiometric = false;
  String _currentLanguage = 'ar';
  bool _isLoadingLanguage = false;

  @override
  void initState() {
    super.initState();
    _loadBiometricStatus();
    _loadCurrentLanguage();

    // تتبع مشاهدة شاشة الإعدادات
    WidgetsBinding.instance.addPostFrameCallback((_) {
      FirebaseAnalyticsService.logScreenView(
        screenName: 'settings_screen',
        screenClass: 'SettingsScreen',
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConfig.lightGrayColorObj,
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  /// بناء شريط التطبيق
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      elevation: 0,
      backgroundColor: AppConfig.primaryColorObj,
      foregroundColor: AppConfig.whiteColorObj,
      title: Text(
        AppLocalizations.of(context).settings,
        style: TextStyle(
          fontSize: AppConfig.titleFontSize,
          fontWeight: FontWeight.bold,
        ),
      ),
      centerTitle: true,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back),
        onPressed: () => Navigator.of(context).pop(),
      ),
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildBody() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(AppConfig.spacing),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رسالة ترحيب
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(AppConfig.largeSpacing),
            decoration: BoxDecoration(
              color: AppConfig.whiteColorObj,
              borderRadius: BorderRadius.circular(AppConfig.largeBorderRadius),
              boxShadow: [
                BoxShadow(
                  color: AppConfig.cardShadowColorObj,
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              children: [
                Icon(
                  Icons.settings,
                  size: 64,
                  color: AppConfig.primaryColorObj,
                ),
                SizedBox(height: AppConfig.spacing),
                Text(
                  AppLocalizations.of(context).appSettings,
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: AppConfig.darkTextColorObj,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: AppConfig.smallSpacing),
                Text(
                  AppLocalizations.of(context).customizeSettings,
                  style: TextStyle(color: AppConfig.secondaryTextColorObj),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),

          SizedBox(height: AppConfig.largeSpacing),

          // قائمة الإعدادات
          _buildSettingsSection(AppLocalizations.of(context).accountSettings, [
            _buildSettingsItem(
              icon: Icons.lock_outline,
              title: AppLocalizations.of(context).changePassword,
              subtitle: AppLocalizations.of(context).changePasswordSubtitle,
              onTap: () => _navigateToChangePassword(),
            ),
          ]),

          SizedBox(height: AppConfig.spacing),

          // إعدادات الأمان
          _buildSettingsSection(AppLocalizations.of(context).securitySettings, [
            _buildBiometricToggle(),
          ]),

          SizedBox(height: AppConfig.spacing),

          // إعدادات المظهر
          _buildSettingsSection(
            AppLocalizations.of(context).appearanceSettings,
            [
              _buildSettingsItem(
                icon: Icons.language,
                title: AppLocalizations.of(context).changeLanguage,
                subtitle: AppLocalizations.of(context).changeLanguageSubtitle,
                onTap: () => _showLanguageDialog(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء قسم الإعدادات
  Widget _buildSettingsSection(String title, List<Widget> items) {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(bottom: AppConfig.spacing),
      decoration: BoxDecoration(
        color: AppConfig.whiteColorObj,
        borderRadius: BorderRadius.circular(AppConfig.largeBorderRadius),
        boxShadow: [
          BoxShadow(
            color: AppConfig.cardShadowColorObj,
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.all(AppConfig.largeSpacing),
            child: Text(
              title,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: AppConfig.darkTextColorObj,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          ...items,
        ],
      ),
    );
  }

  /// بناء عنصر إعدادات
  Widget _buildSettingsItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppConfig.borderRadius),
      child: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: AppConfig.largeSpacing,
          vertical: AppConfig.spacing,
        ),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(AppConfig.smallSpacing),
              decoration: BoxDecoration(
                color: AppConfig.primaryColorObj.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppConfig.borderRadius),
              ),
              child: Icon(icon, color: AppConfig.primaryColorObj, size: 24),
            ),
            SizedBox(width: AppConfig.spacing),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: AppConfig.darkTextColorObj,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppConfig.secondaryTextColorObj,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: AppConfig.secondaryTextColorObj,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  /// الانتقال إلى شاشة تغيير كلمة المرور
  void _navigateToChangePassword() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const ChangePasswordScreen()),
    );
  }

  /// تحميل حالة البصمة
  Future<void> _loadBiometricStatus() async {
    try {
      final isEnabled = await BiometricService.isBiometricEnabled();
      if (mounted) {
        setState(() {
          _isBiometricEnabled = isEnabled;
        });
      }
    } catch (e) {
      // في حالة الخطأ، نفترض أن البصمة غير مفعلة
      if (mounted) {
        setState(() {
          _isBiometricEnabled = false;
        });
      }
    }
  }

  /// بناء مفتاح تبديل البصمة
  Widget _buildBiometricToggle() {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: AppConfig.largeSpacing,
        vertical: AppConfig.spacing,
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(AppConfig.smallSpacing),
            decoration: BoxDecoration(
              color: AppConfig.primaryColorObj.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppConfig.borderRadius),
            ),
            child: Icon(
              Icons.fingerprint,
              color: AppConfig.primaryColorObj,
              size: 24,
            ),
          ),
          SizedBox(width: AppConfig.spacing),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  AppLocalizations.of(context).biometricLogin,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: AppConfig.darkTextColorObj,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 2),
                Text(
                  _isBiometricEnabled
                      ? AppLocalizations.of(context).biometricEnabledSubtitle
                      : AppLocalizations.of(context).biometricDisabledSubtitle,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Color(
                      _isBiometricEnabled
                          ? AppConfig.successColor
                          : AppConfig.secondaryTextColor,
                    ),
                  ),
                ),
              ],
            ),
          ),
          _isLoadingBiometric
              ? SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      AppConfig.primaryColorObj,
                    ),
                  ),
                )
              : Switch(
                  value: _isBiometricEnabled,
                  onChanged: _toggleBiometric,
                  activeThumbColor: AppConfig.primaryColorObj,
                ),
        ],
      ),
    );
  }

  /// تبديل حالة البصمة
  Future<void> _toggleBiometric(bool value) async {
    if (_isLoadingBiometric) return;

    if (value) {
      // تفعيل البصمة - طلب بيانات تسجيل الدخول
      _showBiometricSetupDialog();
    } else {
      // إلغاء تفعيل البصمة
      setState(() {
        _isLoadingBiometric = true;
      });

      try {
        await BiometricService.disableBiometricAuth();

        // تتبع إلغاء تفعيل البصمة
        await FirebaseAnalyticsService.logBiometricAuth(
          action: 'disable',
          success: true,
        );

        if (mounted) {
          setState(() {
            _isBiometricEnabled = false;
          });
          _showSuccessMessage(AppLocalizations.of(context).biometricDisabled);
        }
      } catch (e) {
        // تتبع فشل إلغاء تفعيل البصمة
        await FirebaseAnalyticsService.logBiometricAuth(
          action: 'disable',
          success: false,
          errorCode: e.toString(),
        );

        _showErrorMessage('حدث خطأ أثناء إلغاء تفعيل البصمة: ${e.toString()}');
      } finally {
        if (mounted) {
          setState(() {
            _isLoadingBiometric = false;
          });
        }
      }
    }
  }

  /// عرض رسالة نجاح
  void _showSuccessMessage(String message) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white, size: 20),
            SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: AppConfig.successColorObj,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
        ),
      ),
    );
  }

  /// عرض رسالة خطأ
  void _showErrorMessage(String message) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white, size: 20),
            SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: AppConfig.errorColorObj,
        duration: const Duration(seconds: 4),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
        ),
      ),
    );
  }

  /// تحميل اللغة الحالية
  Future<void> _loadCurrentLanguage() async {
    try {
      final languageProvider = context.read<LanguageProvider>();
      if (mounted) {
        setState(() {
          _currentLanguage = languageProvider.currentLanguageCode;
        });
      }
    } catch (e) {
      // في حالة الخطأ، نستخدم العربية افتراضياً
      if (mounted) {
        setState(() {
          _currentLanguage = 'ar';
        });
      }
    }
  }

  /// عرض حوار اختيار اللغة
  void _showLanguageDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.language, color: AppConfig.primaryColorObj),
            const SizedBox(width: 8),
            Text(AppLocalizations.of(context).selectLanguage),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: LanguageService.supportedLanguages.map((language) {
            return ListTile(
              leading: Text(
                language.flag,
                style: const TextStyle(fontSize: 24),
              ),
              title: Text(language.name),
              subtitle: Text(language.englishName),
              trailing: _currentLanguage == language.code
                  ? Icon(Icons.check_circle, color: AppConfig.successColorObj)
                  : null,
              onTap: () {
                Navigator.of(context).pop();
                _changeLanguage(language.code);
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(AppLocalizations.of(context).cancel),
          ),
        ],
      ),
    );
  }

  /// تغيير اللغة
  Future<void> _changeLanguage(String languageCode) async {
    if (_isLoadingLanguage || _currentLanguage == languageCode) return;

    setState(() {
      _isLoadingLanguage = true;
    });

    try {
      final oldLanguage = _currentLanguage;
      final languageProvider = context.read<LanguageProvider>();
      final success = await languageProvider.changeLanguage(languageCode);

      if (success) {
        // تتبع نجاح تغيير اللغة
        await FirebaseAnalyticsService.logLanguageChange(
          fromLanguage: oldLanguage,
          toLanguage: languageCode,
        );

        setState(() {
          _currentLanguage = languageCode;
        });
        if (mounted) {
          _showSuccessMessage(AppLocalizations.of(context).languageChanged);
        }
        // لا نحتاج لحوار إعادة التشغيل، التغيير فوري
      } else {
        // تتبع فشل تغيير اللغة
        await FirebaseAnalyticsService.logError(
          errorType: 'language_change_failed',
          errorMessage: 'Failed to change language to $languageCode',
          screenName: 'settings_screen',
          functionName: '_changeLanguage',
        );

        if (mounted) {
          _showErrorMessage(AppLocalizations.of(context).languageChangeError);
        }
      }
    } catch (e) {
      _showErrorMessage('حدث خطأ أثناء تغيير اللغة: ${e.toString()}');
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingLanguage = false;
        });
      }
    }
  }

  /// عرض حوار إعداد البصمة مع طلب بيانات تسجيل الدخول
  void _showBiometricSetupDialog() {
    final emailController = TextEditingController();
    final passwordController = TextEditingController();
    final formKey = GlobalKey<FormState>();
    bool obscurePassword = true;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: Row(
            children: [
              Icon(Icons.fingerprint, color: AppConfig.primaryColorObj),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  AppLocalizations.of(context).biometricSetup,
                  style: const TextStyle(fontSize: 18),
                ),
              ),
            ],
          ),
          content: Form(
            key: formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  AppLocalizations.of(context).biometricSetupMessage,
                  style: const TextStyle(fontSize: 14),
                ),
                const SizedBox(height: 16),

                // حقل البريد الإلكتروني
                TextFormField(
                  controller: emailController,
                  keyboardType: TextInputType.emailAddress,
                  decoration: InputDecoration(
                    labelText: AppLocalizations.of(context).enterEmail,
                    prefixIcon: const Icon(Icons.email_outlined),
                    border: const OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return AppLocalizations.of(context).enterEmail;
                    }
                    if (!value.contains('@')) {
                      return AppLocalizations.of(context).validemail;
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 12),

                // حقل كلمة المرور
                TextFormField(
                  controller: passwordController,
                  obscureText: obscurePassword,
                  decoration: InputDecoration(
                    labelText: AppLocalizations.of(context).enterPassword,
                    prefixIcon: const Icon(Icons.lock_outlined),
                    suffixIcon: IconButton(
                      icon: Icon(
                        obscurePassword
                            ? Icons.visibility
                            : Icons.visibility_off,
                      ),
                      onPressed: () {
                        setDialogState(() {
                          obscurePassword = !obscurePassword;
                        });
                      },
                    ),
                    border: const OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return AppLocalizations.of(context).enterPassword;
                    }
                    return null;
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(AppLocalizations.of(context).cancel),
            ),
            ElevatedButton.icon(
              onPressed: () async {
                if (formKey.currentState!.validate()) {
                  Navigator.of(context).pop();
                  await _enableBiometricWithCredentials(
                    emailController.text.trim(),
                    passwordController.text.trim(),
                  );
                }
              },
              icon: const Icon(Icons.fingerprint),
              label: Text(AppLocalizations.of(context).enable),
            ),
          ],
        ),
      ),
    );
  }

  /// تفعيل البصمة مع بيانات تسجيل الدخول
  Future<void> _enableBiometricWithCredentials(
    String email,
    String password,
  ) async {
    setState(() {
      _isLoadingBiometric = true;
    });

    try {
      final result = await BiometricService.enableBiometricAuth(
        email: email,
        password: password,
        serverUrl: AppConfig.defaultServerUrl,
        database: AppConfig.defaultDatabase,
      );

      // تتبع محاولة تفعيل البصمة
      await FirebaseAnalyticsService.logBiometricAuth(
        action: 'enable',
        success: result.success,
        errorCode: result.statusCode,
      );

      if (mounted) {
        if (result.success) {
          setState(() {
            _isBiometricEnabled = true;
          });
          _showSuccessMessage(AppLocalizations.of(context).biometricEnabled);
        } else {
          // عرض رسالة خطأ مفصلة حسب نوع المشكلة
          String errorMessage = result.errorMessage ?? 'فشل في تفعيل البصمة';

          // إضافة نصائح حسب نوع الخطأ
          if (result.statusCode?.contains('NotEnrolled') == true) {
            errorMessage +=
                '\n\nيرجى تسجيل بصمتك أو Face ID في إعدادات الجهاز أولاً';
          } else if (result.statusCode?.contains('NotAvailable') == true) {
            errorMessage += '\n\nتأكد من أن جهازك يدعم البصمة أو Face ID';
          } else if (result.statusCode?.contains('LockedOut') == true) {
            errorMessage += '\n\nيرجى المحاولة مرة أخرى بعد قليل';
          }

          _showErrorMessage(errorMessage);
        }
      }
    } catch (e) {
      // تتبع الأخطاء غير المتوقعة
      await FirebaseAnalyticsService.logBiometricAuth(
        action: 'enable',
        success: false,
        errorCode: 'unexpected_error',
      );

      if (mounted) {
        _showErrorMessage(
          'حدث خطأ غير متوقع أثناء تفعيل البصمة.\nيرجى إعادة المحاولة أو إعادة تشغيل التطبيق.',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingBiometric = false;
        });
      }
    }
  }
}
