import 'package:firebase_performance/firebase_performance.dart';
import 'package:firebase_core/firebase_core.dart';

import '../services/environment_service.dart';
import '../services/firebase_analytics_service.dart';

/// خدمة مراقبة الأداء باستخدام Firebase Performance Monitoring
/// تتيح تتبع أداء التطبيق، سرعة الشبكة، وأوقات الاستجابة
class PerformanceMonitoringService {
  static FirebasePerformance? _performance;
  static bool _isInitialized = false;
  static final Map<String, Trace> _activeTraces = {};

  /// الحصول على مثيل Firebase Performance
  static FirebasePerformance? get performance => _performance;

  /// تهيئة خدمة مراقبة الأداء
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // التحقق من تفعيل Firebase Performance في الإعدادات
      if (!EnvironmentService.isFirebaseEnabled() ||
          !EnvironmentService.isFirebasePerformanceEnabled()) {
        return;
      }

      // تهيئة Firebase Core إذا لم يكن مهيأ
      if (Firebase.apps.isEmpty) {
        await Firebase.initializeApp();
      }

      // تهيئة Performance Monitoring
      _performance = FirebasePerformance.instance;

      // تعيين إعدادات جمع البيانات
      await _performance!.setPerformanceCollectionEnabled(
        EnvironmentService.isPerformanceMonitoringEnabled(),
      );

      _isInitialized = true;
    } catch (e) {}
  }

  /// بدء تتبع عملية مخصصة
  static Future<String?> startTrace(String traceName) async {
    if (_performance == null ||
        !EnvironmentService.isPerformanceMonitoringEnabled()) {
      return null;
    }

    try {
      final trace = _performance!.newTrace(traceName);
      await trace.start();

      final traceId = '${traceName}_${DateTime.now().millisecondsSinceEpoch}';
      _activeTraces[traceId] = trace;

      return traceId;
    } catch (e) {
      return null;
    }
  }

  /// إنهاء تتبع عملية
  static Future<void> stopTrace(
    String? traceId, {
    Map<String, String>? attributes,
    Map<String, int>? metrics,
  }) async {
    if (traceId == null || !_activeTraces.containsKey(traceId)) {
      return;
    }

    try {
      final trace = _activeTraces[traceId]!;

      // إضافة الخصائص
      if (attributes != null) {
        for (final entry in attributes.entries) {
          trace.putAttribute(entry.key, entry.value);
        }
      }

      // إضافة المقاييس
      if (metrics != null) {
        for (final entry in metrics.entries) {
          trace.setMetric(entry.key, entry.value);
        }
      }

      await trace.stop();
      _activeTraces.remove(traceId);
    } catch (e) {}
  }

  /// تتبع طلب HTTP
  static Future<T> traceHttpRequest<T>({
    required String url,
    required String method,
    required Future<T> Function() request,
  }) async {
    if (_performance == null ||
        !EnvironmentService.isPerformanceTraceNetworkRequestsEnabled()) {
      return await request();
    }

    HttpMetric? httpMetric;
    final startTime = DateTime.now();

    try {
      // إنشاء مقياس HTTP
      httpMetric = _performance!.newHttpMetric(
        url,
        HttpMethod.values.firstWhere(
          (m) => m.name.toUpperCase() == method.toUpperCase(),
          orElse: () => HttpMethod.Get,
        ),
      );

      await httpMetric.start();

      // تنفيذ الطلب
      final result = await request();

      // تسجيل النجاح
      httpMetric.httpResponseCode = 200;
      httpMetric.requestPayloadSize = 0;
      httpMetric.responsePayloadSize = 0;

      await httpMetric.stop();

      // تسجيل في Analytics
      final duration = DateTime.now().difference(startTime).inMilliseconds;
      await FirebaseAnalyticsService.logPerformance(
        operationType: 'http_request',
        durationMs: duration,
        success: true,
        details: '$method $url',
      );

      return result;
    } catch (e) {
      // تسجيل الخطأ
      if (httpMetric != null) {
        httpMetric.httpResponseCode = 500;
        await httpMetric.stop();
      }

      final duration = DateTime.now().difference(startTime).inMilliseconds;
      await FirebaseAnalyticsService.logPerformance(
        operationType: 'http_request',
        durationMs: duration,
        success: false,
        details: '$method $url - Error: $e',
      );

      rethrow;
    }
  }

  /// تتبع عملية تحميل الشاشة
  static Future<T> traceScreenLoad<T>({
    required String screenName,
    required Future<T> Function() loadFunction,
  }) async {
    final traceId = await startTrace('screen_load_$screenName');
    final startTime = DateTime.now();

    try {
      final result = await loadFunction();

      final duration = DateTime.now().difference(startTime).inMilliseconds;

      await stopTrace(
        traceId,
        attributes: {'screen_name': screenName, 'success': 'true'},
        metrics: {'load_time_ms': duration},
      );

      // تسجيل في Analytics
      await FirebaseAnalyticsService.logPerformance(
        operationType: 'screen_load',
        durationMs: duration,
        success: true,
        details: screenName,
      );

      return result;
    } catch (e) {
      final duration = DateTime.now().difference(startTime).inMilliseconds;

      await stopTrace(
        traceId,
        attributes: {
          'screen_name': screenName,
          'success': 'false',
          'error': e.toString(),
        },
        metrics: {'load_time_ms': duration},
      );

      // تسجيل في Analytics
      await FirebaseAnalyticsService.logPerformance(
        operationType: 'screen_load',
        durationMs: duration,
        success: false,
        details: '$screenName - Error: $e',
      );

      rethrow;
    }
  }

  /// تتبع عملية قاعدة البيانات
  static Future<T> traceDatabaseOperation<T>({
    required String operationType,
    required String tableName,
    required Future<T> Function() operation,
  }) async {
    final traceId = await startTrace('db_${operationType}_$tableName');
    final startTime = DateTime.now();

    try {
      final result = await operation();

      final duration = DateTime.now().difference(startTime).inMilliseconds;

      await stopTrace(
        traceId,
        attributes: {
          'operation_type': operationType,
          'table_name': tableName,
          'success': 'true',
        },
        metrics: {'duration_ms': duration},
      );

      // تسجيل في Analytics
      await FirebaseAnalyticsService.logPerformance(
        operationType: 'database_operation',
        durationMs: duration,
        success: true,
        details: '$operationType on $tableName',
      );

      return result;
    } catch (e) {
      final duration = DateTime.now().difference(startTime).inMilliseconds;

      await stopTrace(
        traceId,
        attributes: {
          'operation_type': operationType,
          'table_name': tableName,
          'success': 'false',
          'error': e.toString(),
        },
        metrics: {'duration_ms': duration},
      );

      // تسجيل في Analytics
      await FirebaseAnalyticsService.logPerformance(
        operationType: 'database_operation',
        durationMs: duration,
        success: false,
        details: '$operationType on $tableName - Error: $e',
      );

      rethrow;
    }
  }

  /// التحقق من حالة التهيئة
  static bool get isInitialized => _isInitialized && _performance != null;

  /// التحقق من تفعيل مراقبة الأداء
  static bool get isEnabled =>
      EnvironmentService.isFirebaseEnabled() &&
      EnvironmentService.isFirebasePerformanceEnabled();
}
