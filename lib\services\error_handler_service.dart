/// خدمة معالجة الأخطاء المحسنة
class ErrorHandlerService {
  /// معالجة أخطاء الشبكة والاتصال
  static AppError handleNetworkError(dynamic error) {
    final errorString = error.toString().toLowerCase();

    if (errorString.contains('formatexception')) {
      return NetworkError(
        message: 'خطأ في تنسيق البيانات من الخادم',
        details: 'الخادم يرسل بيانات غير صحيحة أو تالفة',
        userMessage: 'يرجى التحقق من إعدادات الخادم أو المحاولة لاحقاً',
        errorCode: 'FORMAT_ERROR',
        originalError: error,
      );
    }

    if (errorString.contains('socketexception') ||
        errorString.contains('connection refused') ||
        errorString.contains('connection failed') ||
        errorString.contains('network unreachable')) {
      return NetworkError(
        message: 'فشل في الاتصال بالخادم',
        details: 'لا يمكن الوصول إلى الخادم المحدد',
        userMessage: 'تحقق من اتصال الإنترنت وعنوان الخادم',
        errorCode: 'CONNECTION_ERROR',
        originalError: error,
      );
    }

    if (errorString.contains('timeoutexception') ||
        errorString.contains('timeout')) {
      return NetworkError(
        message: 'انتهت مهلة الاتصال',
        details: 'الخادم لا يستجيب في الوقت المحدد',
        userMessage: 'تحقق من سرعة الإنترنت أو حاول لاحقاً',
        errorCode: 'TIMEOUT_ERROR',
        originalError: error,
      );
    }

    if (errorString.contains('certificateexception') ||
        errorString.contains('handshake')) {
      return SecurityError(
        message: 'خطأ في شهادة الأمان',
        details: 'فشل في التحقق من شهادة SSL/TLS',
        userMessage: 'مشكلة في أمان الاتصال، تحقق من إعدادات الخادم',
        errorCode: 'CERTIFICATE_ERROR',
        originalError: error,
      );
    }

    // خطأ شبكة عام
    return NetworkError(
      message: 'خطأ في الشبكة',
      details: errorString,
      userMessage: 'حدث خطأ في الاتصال، يرجى المحاولة مرة أخرى',
      errorCode: 'NETWORK_ERROR',
      originalError: error,
    );
  }

  /// معالجة أخطاء المصادقة
  static AppError handleAuthenticationError(dynamic error) {
    final errorString = error.toString().toLowerCase();

    if (errorString.contains('access denied') ||
        errorString.contains('invalid login') ||
        errorString.contains('authentication failed') ||
        errorString.contains('wrong login/password')) {
      return AuthenticationError(
        message: 'فشل في المصادقة',
        details: 'البريد الإلكتروني أو كلمة المرور غير صحيحة',
        userMessage: 'تحقق من البريد الإلكتروني وكلمة المرور',
        errorCode: 'INVALID_CREDENTIALS',
        originalError: error,
      );
    }

    if (errorString.contains('session') ||
        errorString.contains('expired') ||
        errorString.contains('invalid session')) {
      return AuthenticationError(
        message: 'انتهت صلاحية الجلسة',
        details: 'جلسة المستخدم انتهت أو غير صالحة',
        userMessage: 'يرجى تسجيل الدخول مرة أخرى',
        errorCode: 'SESSION_EXPIRED',
        originalError: error,
      );
    }

    if (errorString.contains('permission') ||
        errorString.contains('access forbidden') ||
        errorString.contains('unauthorized')) {
      return AuthenticationError(
        message: 'ليس لديك صلاحية للوصول',
        details: 'المستخدم لا يملك الصلاحيات المطلوبة',
        userMessage: 'تحتاج إلى صلاحيات إضافية للقيام بهذا الإجراء',
        errorCode: 'INSUFFICIENT_PERMISSIONS',
        originalError: error,
      );
    }

    return AuthenticationError(
      message: 'خطأ في المصادقة',
      details: errorString,
      userMessage: 'حدث خطأ في تسجيل الدخول، يرجى المحاولة مرة أخرى',
      errorCode: 'AUTH_ERROR',
      originalError: error,
    );
  }

  /// معالجة أخطاء البيانات
  static AppError handleDataError(dynamic error) {
    final errorString = error.toString().toLowerCase();

    if (errorString.contains('type') && errorString.contains('subtype')) {
      return DataError(
        message: 'خطأ في نوع البيانات',
        details: 'البيانات المستلمة لا تطابق النوع المتوقع',
        userMessage: 'حدث خطأ في معالجة البيانات',
        errorCode: 'TYPE_ERROR',
        originalError: error,
      );
    }

    if (errorString.contains('null') || errorString.contains('empty')) {
      return DataError(
        message: 'بيانات مفقودة',
        details: 'البيانات المطلوبة غير متوفرة أو فارغة',
        userMessage: 'بعض البيانات المطلوبة غير متوفرة',
        errorCode: 'MISSING_DATA',
        originalError: error,
      );
    }

    if (errorString.contains('parse') || errorString.contains('format')) {
      return DataError(
        message: 'خطأ في تحليل البيانات',
        details: 'فشل في تحليل أو تنسيق البيانات',
        userMessage: 'البيانات المستلمة غير صحيحة',
        errorCode: 'PARSE_ERROR',
        originalError: error,
      );
    }

    return DataError(
      message: 'خطأ في البيانات',
      details: errorString,
      userMessage: 'حدث خطأ في معالجة البيانات',
      errorCode: 'DATA_ERROR',
      originalError: error,
    );
  }

  /// معالجة أخطاء التخزين
  static AppError handleStorageError(dynamic error) {
    final errorString = error.toString().toLowerCase();

    if (errorString.contains('encrypt') || errorString.contains('decrypt')) {
      return StorageError(
        message: 'خطأ في التشفير',
        details: 'فشل في تشفير أو فك تشفير البيانات',
        userMessage: 'مشكلة في حفظ البيانات الآمنة',
        errorCode: 'ENCRYPTION_ERROR',
        originalError: error,
      );
    }

    if (errorString.contains('permission') || errorString.contains('access')) {
      return StorageError(
        message: 'ليس لديك صلاحية للوصول للتخزين',
        details: 'التطبيق لا يملك صلاحية الكتابة أو القراءة',
        userMessage: 'تحقق من صلاحيات التطبيق',
        errorCode: 'STORAGE_PERMISSION_ERROR',
        originalError: error,
      );
    }

    if (errorString.contains('space') || errorString.contains('full')) {
      return StorageError(
        message: 'مساحة التخزين ممتلئة',
        details: 'لا توجد مساحة كافية لحفظ البيانات',
        userMessage: 'احذف بعض الملفات لتوفير مساحة',
        errorCode: 'STORAGE_FULL',
        originalError: error,
      );
    }

    return StorageError(
      message: 'خطأ في التخزين',
      details: errorString,
      userMessage: 'حدث خطأ في حفظ أو استرجاع البيانات',
      errorCode: 'STORAGE_ERROR',
      originalError: error,
    );
  }

  /// معالجة أخطاء البصمة
  static AppError handleBiometricError(dynamic error) {
    final errorString = error.toString().toLowerCase();

    if (errorString.contains('notavailable')) {
      return BiometricError(
        message: 'البصمة غير متاحة',
        details: 'الجهاز لا يدعم المصادقة البيومترية',
        userMessage: 'هذا الجهاز لا يدعم البصمة',
        errorCode: 'BIOMETRIC_NOT_AVAILABLE',
        originalError: error,
      );
    }

    if (errorString.contains('notenrolled')) {
      return BiometricError(
        message: 'لا توجد بصمات مسجلة',
        details: 'لم يتم تسجيل أي بصمات على هذا الجهاز',
        userMessage: 'يرجى تسجيل بصمتك في إعدادات الجهاز أولاً',
        errorCode: 'BIOMETRIC_NOT_ENROLLED',
        originalError: error,
      );
    }

    if (errorString.contains('lockedout')) {
      return BiometricError(
        message: 'البصمة مقفلة مؤقتاً',
        details: 'تم قفل البصمة بسبب محاولات فاشلة متعددة',
        userMessage: 'انتظر قليلاً ثم حاول مرة أخرى',
        errorCode: 'BIOMETRIC_LOCKED_OUT',
        originalError: error,
      );
    }

    return BiometricError(
      message: 'خطأ في البصمة',
      details: errorString,
      userMessage: 'حدث خطأ في المصادقة البيومترية',
      errorCode: 'BIOMETRIC_ERROR',
      originalError: error,
    );
  }

  /// تسجيل الأخطاء للمطورين
  static void logError(AppError error, {String? context}) {
    // تسجيل الخطأ في Crashlytics
    _recordErrorInCrashlytics(error, context: context);
  }

  /// تسجيل الخطأ في Firebase Crashlytics
  static Future<void> _recordErrorInCrashlytics(
    AppError error, {
    String? context,
  }) async {
    try {
      // تم إزالة Firebase Crashlytics
    } catch (e) {}
  }

  /// معالجة شاملة للأخطاء
  static AppError handleError(dynamic error, {String? context}) {
    AppError appError;

    // تحديد نوع الخطأ ومعالجته
    if (error is AppError) {
      appError = error;
    } else {
      final errorString = error.toString().toLowerCase();

      if (errorString.contains('socket') ||
          errorString.contains('connection') ||
          errorString.contains('network') ||
          errorString.contains('timeout') ||
          errorString.contains('format')) {
        appError = handleNetworkError(error);
      } else if (errorString.contains('auth') ||
          errorString.contains('login') ||
          errorString.contains('session') ||
          errorString.contains('permission')) {
        appError = handleAuthenticationError(error);
      } else if (errorString.contains('biometric') ||
          errorString.contains('fingerprint') ||
          errorString.contains('face')) {
        appError = handleBiometricError(error);
      } else if (errorString.contains('storage') ||
          errorString.contains('encrypt') ||
          errorString.contains('decrypt')) {
        appError = handleStorageError(error);
      } else if (errorString.contains('type') ||
          errorString.contains('parse') ||
          errorString.contains('data')) {
        appError = handleDataError(error);
      } else {
        // خطأ عام
        appError = GeneralError(
          message: 'حدث خطأ غير متوقع',
          details: error.toString(),
          userMessage: 'حدث خطأ، يرجى المحاولة مرة أخرى',
          errorCode: 'UNKNOWN_ERROR',
          originalError: error,
        );
      }
    }

    // تسجيل الخطأ
    logError(appError, context: context);

    return appError;
  }
}

/// فئة أساسية للأخطاء
abstract class AppError {
  final String message;
  final String details;
  final String userMessage;
  final String errorCode;
  final dynamic originalError;

  AppError({
    required this.message,
    required this.details,
    required this.userMessage,
    required this.errorCode,
    this.originalError,
  });

  @override
  String toString() => '$message: $details';
}

/// أخطاء الشبكة
class NetworkError extends AppError {
  NetworkError({
    required super.message,
    required super.details,
    required super.userMessage,
    required super.errorCode,
    super.originalError,
  });
}

/// أخطاء المصادقة
class AuthenticationError extends AppError {
  AuthenticationError({
    required super.message,
    required super.details,
    required super.userMessage,
    required super.errorCode,
    super.originalError,
  });
}

/// أخطاء البيانات
class DataError extends AppError {
  DataError({
    required super.message,
    required super.details,
    required super.userMessage,
    required super.errorCode,
    super.originalError,
  });
}

/// أخطاء التخزين
class StorageError extends AppError {
  StorageError({
    required super.message,
    required super.details,
    required super.userMessage,
    required super.errorCode,
    super.originalError,
  });
}

/// أخطاء البصمة
class BiometricError extends AppError {
  BiometricError({
    required super.message,
    required super.details,
    required super.userMessage,
    required super.errorCode,
    super.originalError,
  });
}

/// أخطاء الأمان
class SecurityError extends AppError {
  SecurityError({
    required super.message,
    required super.details,
    required super.userMessage,
    required super.errorCode,
    super.originalError,
  });
}

/// أخطاء عامة
class GeneralError extends AppError {
  GeneralError({
    required super.message,
    required super.details,
    required super.userMessage,
    required super.errorCode,
    super.originalError,
  });
}

/// نتيجة عملية المصادقة
class AuthenticationResult {
  final bool isSuccess;
  final int? userId;
  final AppError? error;

  AuthenticationResult._({required this.isSuccess, this.userId, this.error});

  /// نجح في المصادقة
  factory AuthenticationResult.success(int userId) {
    return AuthenticationResult._(isSuccess: true, userId: userId);
  }

  /// فشل في المصادقة
  factory AuthenticationResult.failure(AppError error) {
    return AuthenticationResult._(isSuccess: false, error: error);
  }
}

/// نتيجة عملية عامة
class ServiceResult<T> {
  final bool isSuccess;
  final T? data;
  final AppError? error;

  ServiceResult._({required this.isSuccess, this.data, this.error});

  /// نجحت العملية
  factory ServiceResult.success(T data) {
    return ServiceResult._(isSuccess: true, data: data);
  }

  /// فشلت العملية
  factory ServiceResult.failure(AppError error) {
    return ServiceResult._(isSuccess: false, error: error);
  }
}
