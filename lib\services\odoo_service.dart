import 'package:odoo_rpc/odoo_rpc.dart';
import '../models/public_holiday.dart';
import '../config/app_config.dart';
import 'certificate_pinning_service.dart';
import 'session_manager_service.dart';
import 'error_handler_service.dart';
import 'firebase_crashlytics_service.dart';
import 'performance_monitoring_service.dart';

/// نتيجة طلب الإجازة
class LeaveRequestResult {
  final bool success;
  final int? leaveId;
  final String? errorMessage;

  LeaveRequestResult._({
    required this.success,
    this.leaveId,
    this.errorMessage,
  });

  factory LeaveRequestResult.success(int leaveId) {
    return LeaveRequestResult._(success: true, leaveId: leaveId);
  }

  factory LeaveRequestResult.error(String errorMessage) {
    return LeaveRequestResult._(success: false, errorMessage: errorMessage);
  }
}

/// نتيجة تأكيد طلب الإجازة
class ConfirmLeaveResult {
  final bool success;
  final String? errorMessage;

  ConfirmLeaveResult._({required this.success, this.errorMessage});

  factory ConfirmLeaveResult.success() {
    return ConfirmLeaveResult._(success: true);
  }

  factory ConfirmLeaveResult.error(String errorMessage) {
    return ConfirmLeaveResult._(success: false, errorMessage: errorMessage);
  }
}

/// خدمة للاتصال بـ Odoo عبر JSON-RPC API
class OdooService {
  final String baseUrl;
  final String database;
  late final OdooClient _client;
  OdooSession? _session;
  String? _lastEmail;
  String? _lastPassword;

  OdooService({required this.baseUrl, required this.database}) {
    _client = OdooClient(baseUrl);
  }

  /// التحقق من Certificate Pinning قبل الاتصال
  Future<bool> _verifyCertificatePinning() async {
    if (!AppConfig.enableCertificatePinning) {
      return true; // Certificate Pinning معطل
    }

    try {
      return await CertificatePinningService.checkCertificatePinning(baseUrl);
    } catch (e) {
      return false;
    }
  }

  /// دالة للمصادقة مع Odoo والحصول على UID
  /// [email] البريد الإلكتروني للمستخدم
  /// [password] كلمة المرور
  /// يرجع UID في حالة النجاح أو AppError في حالة الفشل
  Future<AuthenticationResult> authenticate(
    String email,
    String password,
  ) async {
    try {
      // التحقق من Certificate Pinning قبل الاتصال
      final isPinningValid = await _verifyCertificatePinning();
      if (!isPinningValid) {
        final error = SecurityError(
          message: 'فشل في التحقق من Certificate Pinning',
          details: 'شهادة الأمان للخادم غير صحيحة أو منتهية الصلاحية',
          userMessage: 'مشكلة في أمان الاتصال، تحقق من إعدادات الخادم',
          errorCode: 'CERTIFICATE_PINNING_FAILED',
        );
        ErrorHandlerService.logError(error, context: 'المصادقة');
        return AuthenticationResult.failure(error);
      }

      // تتبع أداء المصادقة
      _session = await PerformanceMonitoringService.traceHttpRequest(
        url: baseUrl,
        method: 'POST',
        request: () => _client.authenticate(database, email, password),
      );
      _lastEmail = email;
      _lastPassword = password;

      // بدء جلسة جديدة مع مهلة زمنية
      if (_session?.userId != null && AppConfig.enableSessionTimeout) {
        await SessionManagerService.instance.startSession(_session!.userId);
      }

      return AuthenticationResult.success(_session!.userId);
    } catch (e) {
      final error = ErrorHandlerService.handleError(e, context: 'المصادقة');

      // تسجيل خطأ المصادقة في Crashlytics مع تفاصيل إضافية
      await FirebaseCrashlyticsService.recordAuthError(
        authMethod: 'odoo_rpc',
        errorMessage: error.message,
        errorCode: error.errorCode,
        additionalData: {
          'server_url': baseUrl,
          'database': database,
          'email': email.replaceAll(
            RegExp(r'@.*'),
            '@***',
          ), // إخفاء النطاق للخصوصية
        },
      );

      return AuthenticationResult.failure(error);
    }
  }

  /// دالة لتنفيذ استعلامات على موديلات Odoo
  /// [uid] معرف المستخدم
  /// [password] كلمة المرور
  /// [model] اسم الموديل (مثل hr.employee)
  /// [method] الطريقة المطلوب تنفيذها (مثل search_read)
  /// [args] المعاملات الإضافية
  /// [kwargs] المعاملات المسماة
  Future<dynamic> executeKw({
    required int uid,
    required String password,
    required String model,
    required String method,
    List<dynamic> args = const [],
    Map<String, dynamic> kwargs = const {},
  }) async {
    try {
      // التحقق من Certificate Pinning قبل الاتصال
      final isPinningValid = await _verifyCertificatePinning();
      if (!isPinningValid) {
        return null;
      }

      // التحقق من صحة الجلسة
      if (AppConfig.enableSessionTimeout &&
          !SessionManagerService.instance.isSessionValid()) {
        return null;
      }

      // إذا لم تكن هناك جلسة أو تغير المستخدم، أنشئ جلسة جديدة
      if (_session == null || _session!.userId != uid) {
        if (_lastEmail != null && _lastPassword != null) {
          _session = await _client.authenticate(
            database,
            _lastEmail!,
            _lastPassword!,
          );
        } else {
          return null;
        }
      }

      final result = await _client.callKw({
        'model': model,
        'method': method,
        'args': args,
        'kwargs': kwargs,
      });

      // تسجيل النشاط عند نجاح العملية
      if (AppConfig.enableSessionTimeout) {
        await SessionManagerService.instance.recordActivity();
      }

      return result;
    } catch (e) {
      // معالجة أخطاء محددة مع تفاصيل أكثر
      String errorMessage = 'خطأ في executeKw: $e';
      String errorType = 'unknown_error';

      if (e.toString().contains('FormatException')) {
        errorMessage =
            'خطأ في الاتصال: الخادم لا يستجيب أو يرسل بيانات غير صحيحة.';
        errorType = 'format_error';
      } else if (e.toString().contains('SocketException') ||
          e.toString().contains('Connection refused') ||
          e.toString().contains('Connection failed')) {
        errorMessage = 'خطأ في الاتصال: لا يمكن الوصول إلى الخادم.';
        errorType = 'connection_error';
      } else if (e.toString().contains('TimeoutException')) {
        errorMessage = 'خطأ في الاتصال: انتهت مهلة الاتصال.';
        errorType = 'timeout_error';
      } else if (e.toString().contains('ValidationError')) {
        errorMessage = 'خطأ في البيانات المدخلة: تحقق من صحة المعلومات.';
        errorType = 'validation_error';
      } else if (e.toString().contains('AccessError')) {
        errorMessage = 'خطأ في الصلاحيات: ليس لديك صلاحية للقيام بهذا الإجراء.';
        errorType = 'access_error';
      } else if (e.toString().contains('UserError')) {
        // التحقق من الأخطاء المحددة أولاً
        String errorStr = e.toString().toLowerCase();

        if (errorStr.contains('insufficient') ||
            errorStr.contains('balance') ||
            errorStr.contains('not enough') ||
            errorStr.contains('exceed')) {
          errorMessage = 'لا يوجد رصيد كافي';
        } else if (errorStr.contains('overlap') ||
            errorStr.contains('conflict') ||
            errorStr.contains('already exists') ||
            errorStr.contains('duplicate')) {
          errorMessage = 'أيام متداخلة مع إجازة أخرى';
        } else if (errorStr.contains('date') ||
            errorStr.contains('invalid') ||
            errorStr.contains('wrong')) {
          errorMessage = 'تواريخ غير صحيحة';
        } else {
          // محاولة استخراج رسالة الخطأ من Odoo
          final match = RegExp(r"'([^']*)'").firstMatch(e.toString());
          if (match != null) {
            errorMessage = match.group(1) ?? errorMessage;
          }
        }
        errorType = 'user_error';
      }

      // تسجيل خطأ قاعدة البيانات في Crashlytics مع تفاصيل إضافية
      await FirebaseCrashlyticsService.recordDatabaseError(
        operation: method,
        tableName: model,
        errorMessage: errorMessage,
        additionalData: {
          'args': args.toString(),
          'kwargs': kwargs.toString(),
          'uid': uid,
          'error_type': errorType,
          'original_error': e.toString(),
        },
      );

      // في حالة انتهاء صلاحية الجلسة، حاول إعادة المصادقة
      if (e.toString().contains('session') &&
          _lastEmail != null &&
          _lastPassword != null) {
        try {
          _session = await _client.authenticate(
            database,
            _lastEmail!,
            _lastPassword!,
          );
          final result = await _client.callKw({
            'model': model,
            'method': method,
            'args': args,
            'kwargs': kwargs,
          });
          return result;
        } catch (retryError) {
          // تجاهل الخطأ
        }
      }
      return null;
    }
  }

  /// دالة لجلب بيانات الموظف الحالي
  /// [uid] معرف المستخدم
  /// [password] كلمة المرور
  /// يرجع بيانات الموظف أو null في حالة الفشل
  Future<Map<String, dynamic>?> getCurrentEmployee({
    required int uid,
    required String password,
  }) async {
    try {
      // البحث عن الموظف المرتبط بالمستخدم الحالي
      final employeeData = await executeKw(
        uid: uid,
        password: password,
        model: 'hr.employee',
        method: 'search_read',
        args: [
          [
            ['user_id', '=', uid],
          ], // البحث بناءً على user_id
        ],
        kwargs: {
          'fields': [
            'name',
            'parent_id',
            'connected_with_comp',
            'national_number',
            'resource_calendar_id',
            'image_1920',
          ], // الحقول المطلوبة
          'limit': 1, // موظف واحد فقط
        },
      );

      if (employeeData != null &&
          employeeData is List &&
          employeeData.isNotEmpty) {
        return Map<String, dynamic>.from(employeeData[0]);
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  /// دالة لجلب جميع أنواع الإجازات المتاحة
  /// [uid] معرف المستخدم
  /// [password] كلمة المرور
  /// يرجع قائمة بأنواع الإجازات أو null في حالة الفشل
  Future<List<Map<String, dynamic>>?> getLeaveTypes({
    required int uid,
    required String password,
  }) async {
    try {
      // جلب جميع أنواع الإجازات من hr.leave.type
      final leaveTypesData = await executeKw(
        uid: uid,
        password: password,
        model: 'hr.leave.type',
        method: 'search_read',
        args: [
          [], // بدون شروط للحصول على جميع الأنواع
        ],
        kwargs: {
          'fields': ['name', 'requires_allocation'], // الحقول المطلوبة
          'order': 'name asc', // ترتيب حسب الاسم
        },
      );

      if (leaveTypesData != null && leaveTypesData is List) {
        return List<Map<String, dynamic>>.from(
          leaveTypesData.map((item) => Map<String, dynamic>.from(item)),
        );
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  /// دالة لجلب جدول عمل الموظف
  /// [uid] معرف المستخدم
  /// [password] كلمة المرور
  /// يرجع تفاصيل جدول العمل أو null في حالة الفشل
  Future<Map<String, dynamic>?> getEmployeeWorkingCalendar({
    required int uid,
    required String password,
  }) async {
    try {
      // جلب بيانات الموظف مع جدول العمل
      final employeeData = await getCurrentEmployee(
        uid: uid,
        password: password,
      );

      if (employeeData == null ||
          employeeData['resource_calendar_id'] == null) {
        return null;
      }

      // استخراج معرف جدول العمل
      int calendarId;
      if (employeeData['resource_calendar_id'] is List) {
        final calendarData = employeeData['resource_calendar_id'] as List;
        calendarId = calendarData[0] as int;
      } else {
        calendarId = employeeData['resource_calendar_id'] as int;
      }

      // جلب تفاصيل جدول العمل
      final calendarData = await executeKw(
        uid: uid,
        password: password,
        model: 'resource.calendar',
        method: 'search_read',
        args: [
          [
            ['id', '=', calendarId],
          ],
        ],
        kwargs: {
          'fields': ['name', 'attendance_ids'],
          'limit': 1,
        },
      );

      if (calendarData != null &&
          calendarData is List &&
          calendarData.isNotEmpty) {
        final calendar = calendarData[0];

        // جلب تفاصيل أيام العمل
        final attendanceIds = calendar['attendance_ids'] as List?;
        if (attendanceIds != null && attendanceIds.isNotEmpty) {
          final attendanceData = await executeKw(
            uid: uid,
            password: password,
            model: 'resource.calendar.attendance',
            method: 'search_read',
            args: [
              [
                ['id', 'in', attendanceIds],
              ],
            ],
            kwargs: {
              'fields': ['dayofweek', 'hour_from', 'hour_to'],
            },
          );

          return {
            'calendar_id': calendarId,
            'calendar_name': calendar['name'],
            'attendance': attendanceData ?? [],
          };
        }
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  /// دالة لجلب الإجازات العامة للشركة في فترة زمنية محددة
  /// [uid] معرف المستخدم
  /// [password] كلمة المرور
  /// [dateFrom] تاريخ البداية
  /// [dateTo] تاريخ النهاية
  /// [calendarId] معرف جدول العمل (اختياري)
  /// يرجع قائمة بتواريخ الإجازات العامة أو null في حالة الفشل
  Future<List<DateTime>?> getPublicHolidays({
    required int uid,
    required String password,
    required DateTime dateFrom,
    required DateTime dateTo,
    int? calendarId,
  }) async {
    try {
      // تحويل التواريخ لتشمل اليوم كاملاً
      final searchDateFrom = DateTime(
        dateFrom.year,
        dateFrom.month,
        dateFrom.day,
      );
      final searchDateTo = DateTime(
        dateTo.year,
        dateTo.month,
        dateTo.day,
        23,
        59,
        59,
      );

      // محاولة البحث في resource.calendar.leaves أولاً (بدون شرط calendar_id)
      List<dynamic> searchDomain = [
        '|', // OR condition
        ['resource_id', '=', false],
        ['resource_id', '=', null],
        ['date_from', '<=', searchDateTo.toIso8601String()],
        ['date_to', '>=', searchDateFrom.toIso8601String()],
      ];

      // جلب الإجازات العامة من resource.calendar.leaves
      var publicHolidaysData = await executeKw(
        uid: uid,
        password: password,
        model: 'resource.calendar.leaves',
        method: 'search_read',
        args: [searchDomain],
        kwargs: {
          'fields': [
            'date_from',
            'date_to',
            'name',
            'resource_id',
            'calendar_id',
          ],
          'order': 'date_from asc',
        },
      );

      // إذا لم نجد شيئاً، جرب البحث بدون أي شروط resource_id أو calendar_id
      if (publicHolidaysData == null ||
          (publicHolidaysData is List && publicHolidaysData.isEmpty)) {
        searchDomain = [
          ['date_from', '<=', searchDateTo.toIso8601String()],
          ['date_to', '>=', searchDateFrom.toIso8601String()],
        ];

        publicHolidaysData = await executeKw(
          uid: uid,
          password: password,
          model: 'resource.calendar.leaves',
          method: 'search_read',
          args: [searchDomain],
          kwargs: {
            'fields': [
              'date_from',
              'date_to',
              'name',
              'resource_id',
              'calendar_id',
            ],
            'order': 'date_from asc',
          },
        );
      }

      if (publicHolidaysData == null || publicHolidaysData is! List) {
        return [];
      }

      // تحويل البيانات إلى قائمة تواريخ
      List<DateTime> holidayDates = [];

      for (var holiday in publicHolidaysData) {
        try {
          // التحقق من أن الإجازة عامة (resource_id فارغ أو false)
          final resourceId = holiday['resource_id'];
          if (resourceId != null && resourceId != false) {
            continue;
          }

          final holidayDateFrom = DateTime.parse(
            holiday['date_from'] as String,
          );
          final holidayDateTo = DateTime.parse(holiday['date_to'] as String);

          // إضافة جميع الأيام في فترة الإجازة العامة
          DateTime currentHolidayDate = DateTime(
            holidayDateFrom.year,
            holidayDateFrom.month,
            holidayDateFrom.day,
          );

          final endHolidayDate = DateTime(
            holidayDateTo.year,
            holidayDateTo.month,
            holidayDateTo.day,
          );

          while (currentHolidayDate.isBefore(endHolidayDate) ||
              currentHolidayDate.isAtSameMomentAs(endHolidayDate)) {
            holidayDates.add(currentHolidayDate);

            currentHolidayDate = currentHolidayDate.add(
              const Duration(days: 1),
            );
          }
        } catch (e) {
          // تجاهل الأخطاء في تحليل التواريخ
        }
      }

      return holidayDates;
    } catch (e) {
      return [];
    }
  }

  /// دالة لجلب الإجازات العامة للشركة كنماذج بيانات مفصلة
  /// [uid] معرف المستخدم
  /// [password] كلمة المرور
  /// [dateFrom] تاريخ البداية
  /// [dateTo] تاريخ النهاية
  /// [calendarId] معرف جدول العمل (اختياري)
  /// يرجع قائمة بنماذج الإجازات العامة أو null في حالة الفشل
  Future<List<PublicHoliday>?> getPublicHolidaysDetailed({
    required int uid,
    required String password,
    required DateTime dateFrom,
    required DateTime dateTo,
    int? calendarId,
  }) async {
    try {
      // تحويل التواريخ لتشمل اليوم كاملاً
      final searchDateFrom = DateTime(
        dateFrom.year,
        dateFrom.month,
        dateFrom.day,
      );
      final searchDateTo = DateTime(
        dateTo.year,
        dateTo.month,
        dateTo.day,
        23,
        59,
        59,
      );

      // إعداد شروط البحث للإجازات العامة (بدون شرط calendar_id أولاً)
      List<dynamic> searchDomain = [
        '|', // OR condition
        ['resource_id', '=', false],
        ['resource_id', '=', null],
        ['date_from', '<=', searchDateTo.toIso8601String()],
        ['date_to', '>=', searchDateFrom.toIso8601String()],
      ];

      // جلب الإجازات العامة من resource.calendar.leaves
      var publicHolidaysData = await executeKw(
        uid: uid,
        password: password,
        model: 'resource.calendar.leaves',
        method: 'search_read',
        args: [searchDomain],
        kwargs: {
          'fields': [
            'date_from',
            'date_to',
            'name',
            'calendar_id',
            'resource_id',
          ],
          'order': 'date_from asc',
        },
      );

      // إذا لم نجد شيئاً، جرب البحث بدون أي شروط إضافية
      if (publicHolidaysData == null ||
          (publicHolidaysData is List && publicHolidaysData.isEmpty)) {
        searchDomain = [
          ['date_from', '<=', searchDateTo.toIso8601String()],
          ['date_to', '>=', searchDateFrom.toIso8601String()],
        ];

        publicHolidaysData = await executeKw(
          uid: uid,
          password: password,
          model: 'resource.calendar.leaves',
          method: 'search_read',
          args: [searchDomain],
          kwargs: {
            'fields': [
              'date_from',
              'date_to',
              'name',
              'calendar_id',
              'resource_id',
            ],
            'order': 'date_from asc',
          },
        );
      }

      if (publicHolidaysData == null || publicHolidaysData is! List) {
        return [];
      }

      // تحويل البيانات إلى نماذج PublicHoliday
      List<PublicHoliday> holidays = [];

      for (var holidayData in publicHolidaysData) {
        try {
          // التحقق من أن الإجازة عامة (resource_id فارغ أو false)
          final resourceId = holidayData['resource_id'];
          if (resourceId != null && resourceId != false) {
            continue;
          }

          final holiday = PublicHoliday.fromOdooData(
            Map<String, dynamic>.from(holidayData),
          );
          holidays.add(holiday);
        } catch (e) {}
      }

      return holidays;
    } catch (e) {
      return [];
    }
  }

  /// دالة لحساب أيام العمل الفعلية بين تاريخين مع استثناء الإجازات العامة
  /// [uid] معرف المستخدم
  /// [password] كلمة المرور
  /// [dateFrom] تاريخ البداية
  /// [dateTo] تاريخ النهاية
  /// يرجع عدد أيام العمل الفعلية أو null في حالة الفشل
  Future<double?> calculateWorkingDays({
    required int uid,
    required String password,
    required DateTime dateFrom,
    required DateTime dateTo,
  }) async {
    try {
      // جلب جدول عمل الموظف
      final calendarData = await getEmployeeWorkingCalendar(
        uid: uid,
        password: password,
      );

      if (calendarData == null) {
        // إذا لم يتم العثور على جدول عمل، استخدم الحساب التقليدي مع استثناء الإجازات العامة
        final publicHolidays = await getPublicHolidays(
          uid: uid,
          password: password,
          dateFrom: dateFrom,
          dateTo: dateTo,
        );

        double totalDays = dateTo.difference(dateFrom).inDays + 1.0;
        double publicHolidayDays = publicHolidays?.length.toDouble() ?? 0.0;

        return totalDays - publicHolidayDays;
      }

      // استخراج أيام العمل من جدول العمل
      final attendance = calendarData['attendance'] as List;
      final workingDays = <int>{};

      for (var attendanceRecord in attendance) {
        final dayOfWeek = int.parse(attendanceRecord['dayofweek'].toString());
        workingDays.add(dayOfWeek);
      }

      // جلب الإجازات العامة للفترة المحددة
      final publicHolidays = await getPublicHolidays(
        uid: uid,
        password: password,
        dateFrom: dateFrom,
        dateTo: dateTo,
        calendarId: calendarData['calendar_id'] as int?,
      );

      // تحويل الإجازات العامة إلى Set للبحث السريع
      final publicHolidayDates = <String>{};
      if (publicHolidays != null) {
        for (var holiday in publicHolidays) {
          final dateStr =
              '${holiday.year}-${holiday.month.toString().padLeft(2, '0')}-${holiday.day.toString().padLeft(2, '0')}';
          publicHolidayDates.add(dateStr);
        }
      }

      // حساب أيام العمل الفعلية مع استثناء الإجازات العامة
      double workingDaysCount = 0.0;
      DateTime currentDate = dateFrom;

      while (currentDate.isBefore(dateTo) ||
          currentDate.isAtSameMomentAs(dateTo)) {
        // تحويل التاريخ الحالي إلى نص للمقارنة
        final currentDateStr =
            '${currentDate.year}-${currentDate.month.toString().padLeft(2, '0')}-${currentDate.day.toString().padLeft(2, '0')}';

        // التحقق من أن اليوم ليس إجازة عامة
        if (!publicHolidayDates.contains(currentDateStr)) {
          // في Odoo: 0=الاثنين، 1=الثلاثاء، ... 6=الأحد
          // في Dart: 1=الاثنين، 2=الثلاثاء، ... 7=الأحد
          final odooWeekday = (currentDate.weekday - 1) % 7;

          if (workingDays.contains(odooWeekday)) {
            workingDaysCount += 1.0;
          }
        }

        currentDate = currentDate.add(const Duration(days: 1));
      }

      return workingDaysCount;
    } catch (e) {
      // في حالة الخطأ، استخدم الحساب التقليدي
      return dateTo.difference(dateFrom).inDays + 1.0;
    }
  }

  /// دالة للحصول على معرف الموظف من معرف المستخدم
  /// [uid] معرف المستخدم
  /// [password] كلمة المرور
  /// يرجع معرف الموظف أو null في حالة الفشل
  Future<int?> getEmployeeId({
    required int uid,
    required String password,
  }) async {
    try {
      final employeeData = await executeKw(
        uid: uid,
        password: password,
        model: 'hr.employee',
        method: 'search_read',
        args: [
          [
            ['user_id', '=', uid],
          ],
        ],
        kwargs: {
          'fields': ['id'],
          'limit': 1,
        },
      );

      if (employeeData != null &&
          employeeData is List &&
          employeeData.isNotEmpty) {
        return employeeData[0]['id'] as int;
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  /// دالة لجلب أنواع الإجازات المتاحة للموظف مع التحقق من الرصيد (محسنة للأداء)
  /// [uid] معرف المستخدم
  /// [password] كلمة المرور
  /// يرجع قائمة بأنواع الإجازات المتاحة للموظف أو null في حالة الفشل
  Future<List<Map<String, dynamic>>?> getAvailableLeaveTypesForEmployee({
    required int uid,
    required String password,
  }) async {
    try {
      // تشغيل العمليات الأساسية بشكل متوازي لتوفير الوقت
      final results = await Future.wait([
        getEmployeeId(uid: uid, password: password),
        getLeaveTypes(uid: uid, password: password),
      ]);

      final employeeId = results[0] as int?;
      final allLeaveTypes = results[1] as List<Map<String, dynamic>>?;

      if (employeeId == null || allLeaveTypes == null) {
        return null;
      }

      // تصنيف أنواع الإجازات حسب الحاجة للتخصيص
      List<Map<String, dynamic>> typesNeedingAllocation = [];
      List<Map<String, dynamic>> typesWithoutAllocation = [];

      for (var leaveType in allLeaveTypes) {
        final requiresAllocation = leaveType['requires_allocation'];
        bool needsAllocation = false;

        // التحقق من القيم النصية والمنطقية
        if (requiresAllocation is String) {
          needsAllocation = requiresAllocation.toLowerCase() == 'yes';
        } else if (requiresAllocation is bool) {
          needsAllocation = requiresAllocation == true;
        }

        if (needsAllocation) {
          typesNeedingAllocation.add(leaveType);
        } else {
          // الأنواع التي لا تحتاج تخصيص (No Limit) متاحة دائماً
          typesWithoutAllocation.add(leaveType);
        }
      }

      // جلب أرصدة جميع الأنواع التي تحتاج تخصيص بشكل متوازي
      List<Future<double?>> balanceFutures = typesNeedingAllocation
          .map(
            (leaveType) => _getEmployeeLeaveBalanceForTypeOptimized(
              uid: uid,
              password: password,
              employeeId: employeeId,
              leaveTypeId: leaveType['id'] as int,
            ),
          )
          .toList();

      List<double?> balances = await Future.wait(balanceFutures);

      // تجميع النتائج النهائية
      List<Map<String, dynamic>> availableTypes = [];

      // إضافة الأنواع التي لا تحتاج تخصيص
      availableTypes.addAll(typesWithoutAllocation);

      // إضافة الأنواع التي لها رصيد متاح
      for (int i = 0; i < typesNeedingAllocation.length; i++) {
        final balance = balances[i];
        if (balance != null && balance > 0) {
          availableTypes.add(typesNeedingAllocation[i]);
        }
      }

      return availableTypes;
    } catch (e) {
      return null;
    }
  }

  /// دالة محسنة لجلب رصيد نوع إجازة محدد (بدون استدعاء getEmployeeId مرة أخرى)
  /// [uid] معرف المستخدم
  /// [password] كلمة المرور
  /// [employeeId] معرف الموظف (محفوظ مسبقاً)
  /// [leaveTypeId] معرف نوع الإجازة
  /// يرجع الرصيد المتاح أو null في حالة الفشل
  Future<double?> _getEmployeeLeaveBalanceForTypeOptimized({
    required int uid,
    required String password,
    required int employeeId,
    required int leaveTypeId,
  }) async {
    try {
      // تشغيل استدعاءات جلب الرصيد والأيام المستخدمة بشكل متوازي
      final results = await Future.wait([
        // جلب الرصيد المتاح لهذا النوع
        executeKw(
          uid: uid,
          password: password,
          model: 'hr.leave.allocation',
          method: 'search_read',
          args: [
            [
              ['employee_id', '=', employeeId],
              ['holiday_status_id', '=', leaveTypeId],
              ['state', '=', 'validate'],
            ],
          ],
          kwargs: {
            'fields': ['number_of_days'],
          },
        ),
        // جلب الأيام المستخدمة لهذا النوع
        executeKw(
          uid: uid,
          password: password,
          model: 'hr.leave',
          method: 'search_read',
          args: [
            [
              ['employee_id', '=', employeeId],
              ['holiday_status_id', '=', leaveTypeId],
              [
                'state',
                'in',
                ['validate'],
              ],
            ],
          ],
          kwargs: {
            'fields': ['number_of_days'],
          },
        ),
      ]);

      final allocationData = results[0];
      final usedDaysData = results[1];

      // حساب الرصيد المتاح
      double totalAllocated = 0.0;
      double totalUsed = 0.0;

      if (allocationData != null && allocationData is List) {
        for (var allocation in allocationData) {
          totalAllocated += (allocation['number_of_days'] as num).toDouble();
        }
      }

      if (usedDaysData != null && usedDaysData is List) {
        for (var used in usedDaysData) {
          totalUsed += (used['number_of_days'] as num).toDouble();
        }
      }

      return totalAllocated - totalUsed;
    } catch (e) {
      return null;
    }
  }

  /// دالة لإنشاء طلب إجازة جديد وتقديمه للموافقة
  /// [uid] معرف المستخدم
  /// [password] كلمة المرور
  /// [leaveRequest] بيانات طلب الإجازة
  /// يرجع معرف الطلب الجديد أو رسالة خطأ مفصلة
  Future<LeaveRequestResult> createLeaveRequest({
    required int uid,
    required String password,
    required Map<String, dynamic> leaveRequestData,
  }) async {
    try {
      // إنشاء طلب إجازة جديد في hr.leave
      final result = await executeKw(
        uid: uid,
        password: password,
        model: 'hr.leave',
        method: 'create',
        args: [leaveRequestData],
        kwargs: {},
      );

      if (result != null) {
        if (result is int) {
          // نجح الإنشاء، الآن نقدم الطلب للموافقة
          final confirmResult = await _confirmLeaveRequest(
            uid: uid,
            password: password,
            leaveId: result,
          );

          if (confirmResult.success) {
            return LeaveRequestResult.success(result);
          } else {
            return LeaveRequestResult.error(
              'تم إنشاء الطلب ولكن فشل في تقديمه للموافقة: ${confirmResult.errorMessage}',
            );
          }
        } else if (result is List && result.isNotEmpty) {
          // أحياناً ترجع Odoo قائمة بالمعرفات
          final leaveId = result[0] as int;

          final confirmResult = await _confirmLeaveRequest(
            uid: uid,
            password: password,
            leaveId: leaveId,
          );

          if (confirmResult.success) {
            return LeaveRequestResult.success(leaveId);
          } else {
            return LeaveRequestResult.error(
              'تم إنشاء الطلب ولكن فشل في تقديمه للموافقة: ${confirmResult.errorMessage}',
            );
          }
        }
      }

      return LeaveRequestResult.error(
        'فشل في إنشاء طلب الإجازة - لم يتم إرجاع معرف صحيح',
      );
    } catch (e) {
      String errorMessage = 'خطأ غير متوقع في إنشاء طلب الإجازة';

      // التحقق من الأخطاء المحددة أولاً
      String errorStr = e.toString().toLowerCase();

      if (errorStr.contains('insufficient') ||
          errorStr.contains('balance') ||
          errorStr.contains('not enough') ||
          errorStr.contains('exceed')) {
        errorMessage = 'لا يوجد رصيد كافي';
      } else if (errorStr.contains('overlap') ||
          errorStr.contains('conflict') ||
          errorStr.contains('already exists') ||
          errorStr.contains('duplicate')) {
        errorMessage = 'أيام متداخلة مع إجازة أخرى';
      } else if (errorStr.contains('date') ||
          errorStr.contains('invalid') ||
          errorStr.contains('wrong') ||
          errorStr.contains('validation')) {
        errorMessage = 'تواريخ غير صحيحة';
      } else {
        // استخراج رسالة الخطأ من Odoo
        final userErrorMatch = RegExp(r"'([^']*)'").firstMatch(e.toString());
        if (userErrorMatch != null) {
          errorMessage = userErrorMatch.group(1) ?? errorMessage;
        }
      }

      return LeaveRequestResult.error(errorMessage);
    }
  }

  /// دالة لتقديم طلب الإجازة للموافقة
  /// [uid] معرف المستخدم
  /// [password] كلمة المرور
  /// [leaveId] معرف طلب الإجازة
  /// يرجع نتيجة مفصلة للتأكيد
  Future<ConfirmLeaveResult> _confirmLeaveRequest({
    required int uid,
    required String password,
    required int leaveId,
  }) async {
    try {
      // استدعاء دالة action_confirm لتقديم الطلب
      final result = await executeKw(
        uid: uid,
        password: password,
        model: 'hr.leave',
        method: 'action_confirm',
        args: [
          [leaveId],
        ], // قائمة بمعرفات الطلبات
        kwargs: {},
      );

      if (result != null) {
        return ConfirmLeaveResult.success();
      } else {
        return ConfirmLeaveResult.error('فشل في تقديم الطلب للموافقة');
      }
    } catch (e) {
      String errorMessage = 'خطأ في تقديم الطلب للموافقة';

      if (e.toString().contains('ValidationError')) {
        errorMessage = 'خطأ في بيانات الطلب - لا يمكن تقديمه للموافقة';
      } else if (e.toString().contains('AccessError')) {
        errorMessage = 'ليس لديك صلاحية لتقديم هذا الطلب';
      } else if (e.toString().contains('UserError')) {
        // استخراج رسالة الخطأ من Odoo
        final match = RegExp(r"'([^']*)'").firstMatch(e.toString());
        if (match != null) {
          errorMessage = match.group(1) ?? errorMessage;
        }
      }

      return ConfirmLeaveResult.error(errorMessage);
    }
  }

  /// دالة لجلب طلبات الإجازة الخاصة بالموظف
  /// [uid] معرف المستخدم
  /// [password] كلمة المرور
  /// يرجع قائمة بطلبات الإجازة أو null في حالة الفشل
  Future<List<Map<String, dynamic>>?> getEmployeeLeaveRequests({
    required int uid,
    required String password,
  }) async {
    try {
      // الحصول على معرف الموظف أولاً
      final employeeId = await getEmployeeId(uid: uid, password: password);
      if (employeeId == null) {
        return null;
      }

      // جلب طلبات الإجازة للموظف
      final leaveRequestsData = await executeKw(
        uid: uid,
        password: password,
        model: 'hr.leave',
        method: 'search_read',
        args: [
          [
            ['employee_id', '=', employeeId],
          ],
        ],
        kwargs: {
          'fields': [
            'id',
            'holiday_status_id',
            'request_date_from',
            'request_date_to',
            'date_from',
            'date_to',
            'number_of_days',
            'state',
            'create_date',
          ],
          'order': 'create_date desc', // الأحدث أولاً
        },
      );

      if (leaveRequestsData != null && leaveRequestsData is List) {
        return List<Map<String, dynamic>>.from(
          leaveRequestsData.map((item) => Map<String, dynamic>.from(item)),
        );
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  /// دالة لجلب رصيد نوع إجازة محدد للموظف (محسنة للأداء)
  /// [uid] معرف المستخدم
  /// [password] كلمة المرور
  /// [leaveTypeId] معرف نوع الإجازة
  /// يرجع الرصيد المتاح أو null في حالة الفشل
  Future<double?> getEmployeeLeaveBalanceForType({
    required int uid,
    required String password,
    required int leaveTypeId,
  }) async {
    try {
      // الحصول على معرف الموظف أولاً
      final employeeId = await getEmployeeId(uid: uid, password: password);
      if (employeeId == null) {
        return null;
      }

      // استخدام الدالة المحسنة
      return await _getEmployeeLeaveBalanceForTypeOptimized(
        uid: uid,
        password: password,
        employeeId: employeeId,
        leaveTypeId: leaveTypeId,
      );
    } catch (e) {
      return null;
    }
  }

  /// دالة لجلب رصيد الإجازات المتاح للموظف
  /// [uid] معرف المستخدم
  /// [password] كلمة المرور
  /// يرجع رصيد الإجازات أو null في حالة الفشل
  Future<Map<String, double>?> getEmployeeLeaveBalance({
    required int uid,
    required String password,
  }) async {
    try {
      // الحصول على معرف الموظف أولاً
      final employeeId = await getEmployeeId(uid: uid, password: password);
      if (employeeId == null) {
        return null;
      }

      // جلب جميع أنواع الإجازات المتاحة
      final leaveTypesData = await executeKw(
        uid: uid,
        password: password,
        model: 'hr.leave.type',
        method: 'search_read',
        args: [[]],
        kwargs: {
          'fields': ['id', 'name', 'requires_allocation'],
        },
      );

      if (leaveTypesData == null || leaveTypesData is! List) {
        return null;
      }

      // تصفية الأنواع التي تحتاج إلى تخصيص
      final filteredTypes = leaveTypesData.where((leaveType) {
        final requiresAllocation = leaveType['requires_allocation'];
        // التحقق من القيم النصية والمنطقية
        if (requiresAllocation is String) {
          return requiresAllocation.toLowerCase() == 'yes';
        } else if (requiresAllocation is bool) {
          return requiresAllocation == true;
        }
        return false;
      }).toList();

      Map<String, double> balances = {};

      // جلب الرصيد لكل نوع إجازة مفلتر
      for (var leaveType in filteredTypes) {
        final leaveTypeId = leaveType['id'] as int;
        final leaveTypeName = leaveType['name'] as String;

        // جلب الرصيد المتاح لهذا النوع
        final allocationData = await executeKw(
          uid: uid,
          password: password,
          model: 'hr.leave.allocation',
          method: 'search_read',
          args: [
            [
              ['employee_id', '=', employeeId],
              ['holiday_status_id', '=', leaveTypeId],
              ['state', '=', 'validate'],
            ],
          ],
          kwargs: {
            'fields': ['number_of_days'],
          },
        );

        // جلب الأيام المستخدمة لهذا النوع
        final usedDaysData = await executeKw(
          uid: uid,
          password: password,
          model: 'hr.leave',
          method: 'search_read',
          args: [
            [
              ['employee_id', '=', employeeId],
              ['holiday_status_id', '=', leaveTypeId],
              [
                'state',
                'in',
                ['validate'],
              ],
            ],
          ],
          kwargs: {
            'fields': ['number_of_days'],
          },
        );

        // حساب الرصيد المتاح
        double totalAllocated = 0.0;
        double totalUsed = 0.0;

        if (allocationData != null && allocationData is List) {
          for (var allocation in allocationData) {
            totalAllocated += (allocation['number_of_days'] as num).toDouble();
          }
        }

        if (usedDaysData != null && usedDaysData is List) {
          for (var used in usedDaysData) {
            totalUsed += (used['number_of_days'] as num).toDouble();
          }
        }

        double availableBalance = totalAllocated - totalUsed;
        if (availableBalance > 0) {
          balances[leaveTypeName] = availableBalance;
        }
      }

      return balances;
    } catch (e) {
      return null;
    }
  }

  /// التحقق من كون المستخدم مدير إجازات
  /// [uid] معرف المستخدم
  /// [password] كلمة المرور
  /// يرجع true إذا كان المستخدم مدير إجازات
  Future<bool> isLeaveManager({
    required int uid,
    required String password,
  }) async {
    try {
      // البحث عن الموظفين الذين يديرهم هذا المستخدم
      final employeesData = await executeKw(
        uid: uid,
        password: password,
        model: 'hr.employee',
        method: 'search_read',
        args: [
          [
            ['leave_manager_id', '=', uid],
          ],
        ],
        kwargs: {
          'fields': ['id', 'name'],
          'limit': 1,
        },
      );

      return employeesData != null &&
          employeesData is List &&
          employeesData.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// جلب جميع طلبات الإجازة للموظفين تحت إدارة المدير
  /// [uid] معرف المستخدم (المدير)
  /// [password] كلمة المرور
  /// يرجع قائمة بجميع طلبات الإجازة (معلقة، موافق عليها، مرفوضة)
  Future<List<Map<String, dynamic>>> getPendingLeaveRequests({
    required int uid,
    required String password,
  }) async {
    try {
      // جلب جميع طلبات الإجازة للموظفين تحت إدارة هذا المدير
      final leaveRequestsData = await executeKw(
        uid: uid,
        password: password,
        model: 'hr.leave',
        method: 'search_read',
        args: [
          [
            ['employee_id.leave_manager_id', '=', uid],
            [
              'state',
              'in',
              ['confirm', 'validate1', 'validate', 'refuse'],
            ], // جميع الحالات (معلقة، موافقة أولى، معتمدة نهائياً، مرفوضة)
          ],
        ],
        kwargs: {
          'fields': [
            'id',
            'employee_id',
            'holiday_status_id',
            'request_date_from',
            'request_date_to',
            'number_of_days',
            'name',
            'state',
            'date_from',
            'date_to',
          ],
          'order': 'request_date_from desc',
        },
      );

      if (leaveRequestsData != null && leaveRequestsData is List) {
        return List<Map<String, dynamic>>.from(leaveRequestsData);
      }

      return [];
    } catch (e) {
      return [];
    }
  }

  /// الموافقة على طلب إجازة
  /// [uid] معرف المستخدم (المدير)
  /// [password] كلمة المرور
  /// [leaveId] معرف طلب الإجازة
  /// يرجع true في حالة النجاح
  Future<bool> approveLeaveRequest({
    required int uid,
    required String password,
    required int leaveId,
  }) async {
    try {
      final result = await executeKw(
        uid: uid,
        password: password,
        model: 'hr.leave',
        method: 'action_approve',
        args: [
          [leaveId],
        ],
      );

      return result != null;
    } catch (e) {
      return false;
    }
  }

  /// رفض طلب إجازة
  /// [uid] معرف المستخدم (المدير)
  /// [password] كلمة المرور
  /// [leaveId] معرف طلب الإجازة
  /// يرجع true في حالة النجاح
  Future<bool> rejectLeaveRequest({
    required int uid,
    required String password,
    required int leaveId,
  }) async {
    try {
      // تغيير حالة الطلب إلى refuse مباشرة (بدون action_refuse)
      await executeKw(
        uid: uid,
        password: password,
        model: 'hr.leave',
        method: 'write',
        args: [
          [leaveId],
          {'state': 'refuse'},
        ],
      );

      return true;
    } catch (e) {
      return false;
    }
  }

  /// إنهاء الجلسة وتسجيل الخروج
  Future<void> logout() async {
    try {
      // إنهاء جلسة إدارة الجلسات
      if (AppConfig.enableSessionTimeout) {
        await SessionManagerService.instance.endSession();
      }

      // مسح بيانات الجلسة المحلية
      _session = null;
      _lastEmail = null;
      _lastPassword = null;
    } catch (e) {}
  }
}
