import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import '../services/environment_service.dart';

/// خدمة Firebase Analytics لمراقبة استخدام التطبيق وسلوك المستخدمين
/// تتيح تتبع الأحداث، الشاشات، وخصائص المستخدمين بطريقة آمنة ومتوافقة مع GDPR
class FirebaseAnalyticsService {
  static FirebaseAnalytics? _analytics;
  static FirebaseAnalyticsObserver? _observer;
  static bool _isInitialized = false;

  /// الحصول على مثيل Firebase Analytics
  static FirebaseAnalytics? get analytics => _analytics;

  /// الحصول على مراقب Firebase Analytics للتنقل
  static FirebaseAnalyticsObserver? get observer => _observer;

  /// تهيئة Firebase Analytics
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // التحقق من تفعيل Firebase في الإعدادات
      if (!EnvironmentService.isFirebaseEnabled() ||
          !EnvironmentService.isFirebaseAnalyticsEnabled()) {
        if (kDebugMode) {
          debugPrint('🔥 Firebase Analytics معطل في الإعدادات');
        }
        return;
      }

      // تهيئة Firebase Core إذا لم يكن مهيأ
      if (Firebase.apps.isEmpty) {
        await Firebase.initializeApp();
      }

      // تهيئة Analytics
      _analytics = FirebaseAnalytics.instance;
      _observer = FirebaseAnalyticsObserver(analytics: _analytics!);

      // تعيين خصائص التطبيق الأساسية
      await _setDefaultUserProperties();

      // تعيين وضع التشخيص
      await _analytics!.setAnalyticsCollectionEnabled(
        EnvironmentService.isAnalyticsCollectCustomEventsEnabled(),
      );

      _isInitialized = true;
    } catch (e) {}
  }

  /// تعيين خصائص المستخدم الافتراضية
  static Future<void> _setDefaultUserProperties() async {
    if (_analytics == null ||
        !EnvironmentService.isAnalyticsCollectUserPropertiesEnabled()) {
      return;
    }

    try {
      await _analytics!.setUserProperty(
        name: 'app_version',
        value: EnvironmentService.getAppVersion(),
      );

      await _analytics!.setUserProperty(
        name: 'app_environment',
        value: kDebugMode ? 'development' : 'production',
      );

      await _analytics!.setUserProperty(
        name: 'platform',
        value: defaultTargetPlatform.name,
      );
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في تعيين خصائص المستخدم: $e');
      }
    }
  }

  /// تسجيل حدث مخصص
  static Future<void> logEvent({
    required String name,
    Map<String, Object>? parameters,
  }) async {
    if (_analytics == null ||
        !EnvironmentService.isAnalyticsCollectCustomEventsEnabled()) {
      return;
    }

    try {
      await _analytics!.logEvent(name: name, parameters: parameters);
    } catch (e) {}
  }

  /// تسجيل مشاهدة شاشة
  static Future<void> logScreenView({
    required String screenName,
    String? screenClass,
    Map<String, Object>? parameters,
  }) async {
    if (_analytics == null ||
        !EnvironmentService.isAnalyticsCollectScreenViewsEnabled()) {
      return;
    }

    try {
      await _analytics!.logScreenView(
        screenName: screenName,
        screenClass: screenClass,
        parameters: parameters,
      );
    } catch (e) {}
  }

  /// تسجيل تسجيل الدخول
  static Future<void> logLogin({
    required String method,
    bool? success,
    String? errorCode,
  }) async {
    final parameters = <String, Object>{'login_method': method};

    if (success != null) {
      parameters['success'] = success ? 'true' : 'false';
    }

    if (errorCode != null) {
      parameters['error_code'] = errorCode;
    }

    await logEvent(name: 'login', parameters: parameters);
  }

  /// تسجيل تسجيل الخروج
  static Future<void> logLogout({
    required String method,
    bool? dataCleared,
  }) async {
    final parameters = <String, Object>{'logout_method': method};

    if (dataCleared != null) {
      parameters['data_cleared'] = dataCleared ? 'true' : 'false';
    }

    await logEvent(name: 'logout', parameters: parameters);
  }

  /// تسجيل طلب إجازة
  static Future<void> logLeaveRequest({
    required String leaveType,
    required int daysRequested,
    required String status,
  }) async {
    await logEvent(
      name: 'leave_request_submitted',
      parameters: {
        'leave_type': leaveType,
        'days_requested': daysRequested,
        'status': status,
      },
    );
  }

  /// تسجيل موافقة على إجازة
  static Future<void> logLeaveApproval({
    required String action,
    required String leaveType,
    required int daysRequested,
  }) async {
    await logEvent(
      name: 'leave_approval_action',
      parameters: {
        'action': action, // approve, reject, validate
        'leave_type': leaveType,
        'days_requested': daysRequested,
      },
    );
  }

  /// تسجيل تغيير كلمة المرور
  static Future<void> logPasswordChange({
    required bool success,
    String? errorCode,
  }) async {
    await logEvent(
      name: 'password_change',
      parameters: {
        'success': success ? 'true' : 'false',
        if (errorCode != null) 'error_code': errorCode,
      },
    );
  }

  /// تسجيل استخدام المصادقة البيومترية
  static Future<void> logBiometricAuth({
    required String action, // enable, disable, authenticate
    required bool success,
    String? errorCode,
  }) async {
    await logEvent(
      name: 'biometric_auth',
      parameters: {
        'action': action,
        'success': success ? 'true' : 'false',
        if (errorCode != null) 'error_code': errorCode,
      },
    );
  }

  /// تسجيل تغيير اللغة
  static Future<void> logLanguageChange({
    required String fromLanguage,
    required String toLanguage,
  }) async {
    await logEvent(
      name: 'language_change',
      parameters: {'from_language': fromLanguage, 'to_language': toLanguage},
    );
  }

  /// تسجيل خطأ في التطبيق
  static Future<void> logError({
    required String errorType,
    required String errorMessage,
    String? screenName,
    String? functionName,
  }) async {
    await logEvent(
      name: 'app_error',
      parameters: {
        'error_type': errorType,
        'error_message': errorMessage,
        if (screenName != null) 'screen_name': screenName,
        if (functionName != null) 'function_name': functionName,
      },
    );
  }

  /// تسجيل أداء العمليات
  static Future<void> logPerformance({
    required String operationType,
    required int durationMs,
    bool? success,
    String? details,
  }) async {
    await logEvent(
      name: 'performance_metric',
      parameters: {
        'operation_type': operationType,
        'duration_ms': durationMs,
        if (success != null) 'success': success ? 'true' : 'false',
        if (details != null) 'details': details,
      },
    );
  }

  /// تعيين معرف المستخدم (بدون معلومات شخصية)
  static Future<void> setUserId(String? userId) async {
    if (_analytics == null ||
        !EnvironmentService.isAnalyticsCollectUserPropertiesEnabled()) {
      return;
    }

    try {
      // استخدام hash للمعرف لحماية الخصوصية
      final hashedUserId = userId?.hashCode.toString();
      await _analytics!.setUserId(id: hashedUserId);
    } catch (e) {}
  }

  /// تعيين خصائص مخصصة للمستخدم
  static Future<void> setUserProperty({
    required String name,
    required String? value,
  }) async {
    if (_analytics == null ||
        !EnvironmentService.isAnalyticsCollectUserPropertiesEnabled()) {
      return;
    }

    try {
      await _analytics!.setUserProperty(name: name, value: value);

      if (kDebugMode) {
        debugPrint('🏷️ تم تعيين خاصية المستخدم: $name = $value');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في تعيين خاصية المستخدم $name: $e');
      }
    }
  }

  /// إعادة تعيين بيانات Analytics (عند تسجيل الخروج)
  static Future<void> resetAnalyticsData() async {
    if (_analytics == null) return;

    try {
      await _analytics!.resetAnalyticsData();

      if (kDebugMode) {
        debugPrint('🔄 تم إعادة تعيين بيانات Analytics');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في إعادة تعيين بيانات Analytics: $e');
      }
    }
  }

  /// التحقق من حالة التهيئة
  static bool get isInitialized => _isInitialized && _analytics != null;

  /// التحقق من تفعيل Analytics
  static bool get isEnabled =>
      EnvironmentService.isFirebaseEnabled() &&
      EnvironmentService.isFirebaseAnalyticsEnabled();
}
