import 'package:flutter/material.dart';
import '../config/app_config.dart';
import '../widgets/session_timeout_dialog.dart';
import '../screens/login_screen.dart';
import 'session_manager_service.dart';

/// خدمة تسجيل الخروج التلقائي
/// تدير تسجيل الخروج التلقائي عند انتهاء مهلة الجلسة
class AutoLogoutService {
  static AutoLogoutService? _instance;
  static AutoLogoutService get instance {
    _instance ??= AutoLogoutService._internal();
    return _instance!;
  }

  AutoLogoutService._internal();

  /// مفتاح التنقل العام للتطبيق
  GlobalKey<NavigatorState>? _navigatorKey;

  /// حالة تهيئة الخدمة
  bool _isInitialized = false;

  /// حالة عرض تنبيه انتهاء الجلسة
  bool _isWarningShown = false;

  /// تهيئة خدمة تسجيل الخروج التلقائي
  Future<void> initialize(GlobalKey<NavigatorState> navigatorKey) async {
    if (_isInitialized) return;

    try {
      _navigatorKey = navigatorKey;

      // تهيئة خدمة إدارة الجلسات
      await SessionManagerService.instance.initialize();

      // تعيين callbacks لأحداث الجلسة
      _setupSessionCallbacks();

      _isInitialized = true;
    } catch (e) {}
  }

  /// إعداد callbacks لأحداث الجلسة
  void _setupSessionCallbacks() {
    // تنبيه انتهاء الجلسة
    SessionManagerService.instance.setOnSessionWarning(() {
      _showSessionWarning();
    });

    // انتهاء الجلسة
    SessionManagerService.instance.setOnSessionExpired(() {
      _handleSessionExpired();
    });

    // تجديد النشاط
    SessionManagerService.instance.setOnActivityDetected(() {
      _isWarningShown = false; // إعادة تعيين حالة التنبيه
    });
  }

  /// عرض تنبيه انتهاء الجلسة
  void _showSessionWarning() {
    if (!AppConfig.enableSessionWarnings || _isWarningShown) return;

    final context = _navigatorKey?.currentContext;
    if (context == null) return;

    _isWarningShown = true;

    SessionTimeoutManager.showWarning(
      context,
      onExtendSession: () {
        _extendSession();
      },
      onLogout: () {
        _performLogout();
      },
      useDialog: true, // استخدام مربع حوار للتنبيه المهم
    );
  }

  /// معالجة انتهاء الجلسة
  void _handleSessionExpired() {
    if (!AppConfig.enableAutoLogout) {
      return;
    }

    _performLogout();
  }

  /// تمديد الجلسة
  void _extendSession() {
    try {
      // تسجيل نشاط جديد لتمديد الجلسة
      SessionManagerService.instance.recordActivity();

      _isWarningShown = false;

      // عرض رسالة تأكيد
      _showExtensionConfirmation();
    } catch (e) {}
  }

  /// عرض رسالة تأكيد تمديد الجلسة
  void _showExtensionConfirmation() {
    final context = _navigatorKey?.currentContext;
    if (context == null) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Text(
              'تم تمديد الجلسة لمدة ${SessionManagerService.instance.getSessionTimeoutMinutes()} دقيقة إضافية',
              style: const TextStyle(fontSize: 14, color: Colors.white),
            ),
          ],
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// تنفيذ تسجيل الخروج
  void _performLogout() async {
    try {
      final context = _navigatorKey?.currentContext;
      if (context == null) return;

      // إنهاء الجلسة
      await SessionManagerService.instance.endSession();

      // مسح البيانات المحفوظة (اختياري - حسب تفضيل المستخدم)
      // await StorageService.clearLoginCredentials();

      // عرض رسالة تسجيل الخروج
      _showLogoutMessage();

      // الانتقال إلى شاشة تسجيل الدخول
      if (context.mounted) {
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => const LoginScreen()),
          (route) => false,
        );
      }
    } catch (e) {}
  }

  /// عرض رسالة تسجيل الخروج
  void _showLogoutMessage() {
    final context = _navigatorKey?.currentContext;
    if (context == null) return;

    // عرض رسالة مؤقتة قبل الانتقال
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.info, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'تم تسجيل الخروج تلقائياً بسبب انتهاء مهلة الجلسة',
                style: const TextStyle(fontSize: 14, color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.blue,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// تسجيل نشاط المستخدم يدوياً
  void recordUserActivity() {
    if (!_isInitialized || !AppConfig.enableSessionTimeout) return;

    SessionManagerService.instance.recordActivity();
  }

  /// الحصول على معلومات الجلسة الحالية
  Map<String, dynamic> getSessionInfo() {
    if (!_isInitialized) {
      return {'error': 'Service not initialized'};
    }

    return SessionManagerService.instance.getSessionInfo();
  }

  /// التحقق من صحة الجلسة الحالية
  bool isSessionValid() {
    if (!_isInitialized || !AppConfig.enableSessionTimeout) return true;

    return SessionManagerService.instance.isSessionValid();
  }

  /// إعادة تعيين حالة التنبيه
  void resetWarningState() {
    _isWarningShown = false;
  }

  /// تنظيف الخدمة
  void dispose() {
    SessionManagerService.instance.dispose();
    _navigatorKey = null;
    _isInitialized = false;
    _isWarningShown = false;
  }
}

/// Mixin لإضافة دعم تسجيل الخروج التلقائي للشاشات
mixin AutoLogoutMixin<T extends StatefulWidget> on State<T> {
  @override
  void initState() {
    super.initState();

    // تسجيل النشاط عند فتح الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      AutoLogoutService.instance.recordUserActivity();
    });
  }

  /// تسجيل نشاط المستخدم
  void recordActivity() {
    AutoLogoutService.instance.recordUserActivity();
  }

  /// التحقق من صحة الجلسة قبل العمليات الحساسة
  bool checkSessionValidity() {
    return AutoLogoutService.instance.isSessionValid();
  }
}
