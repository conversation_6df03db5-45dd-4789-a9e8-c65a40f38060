import 'package:flutter/material.dart';
import '../services/odoo_service.dart';
import '../services/storage_service.dart';
import '../services/biometric_service.dart';
import '../services/firebase_analytics_service.dart';
import '../services/firebase_crashlytics_service.dart';
import '../services/performance_monitoring_service.dart';
import '../services/error_handler_service.dart';
import '../config/app_config.dart';
import '../generated/l10n/app_localizations.dart';
import 'employee_screen.dart';

/// شاشة تسجيل الدخول المصرفية الحديثة
class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _emailFocusNode = FocusNode();
  final _passwordFocusNode = FocusNode();

  bool _isLoading = false;
  bool _obscurePassword = true;
  bool _rememberMe = false;
  bool _biometricEnabled = false;
  String? _errorMessage;

  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadSavedCredentials();

    // تتبع مشاهدة شاشة تسجيل الدخول
    WidgetsBinding.instance.addPostFrameCallback((_) {
      FirebaseAnalyticsService.logScreenView(
        screenName: 'login_screen',
        screenClass: 'LoginScreen',
      );
    });
  }

  /// تحميل بيانات تسجيل الدخول المحفوظة
  Future<void> _loadSavedCredentials() async {
    try {
      final credentials = await StorageService.getLoginCredentials();

      if (credentials['rememberMe'] == 'true') {
        setState(() {
          _emailController.text = credentials['email'] ?? '';
          _passwordController.text = credentials['password'] ?? '';
          _rememberMe = true;
        });
      }

      // التحقق من توفر البصمة
      await _checkBiometricAvailability();
    } catch (e) {
      // في حالة حدوث خطأ، لا نفعل شيئاً
      debugPrint('خطأ في تحميل البيانات المحفوظة: $e');
    }
  }

  /// التحقق من توفر المصادقة البيومترية
  Future<void> _checkBiometricAvailability() async {
    try {
      final isEnabled = await BiometricService.isBiometricEnabled();

      setState(() {
        _biometricEnabled = isEnabled;

        // إذا تم تفعيل البصمة وكان "تذكر البيانات" مفعلاً، قم بإلغائه
        if (isEnabled && _rememberMe) {
          _rememberMe = false;

          // حفظ التغيير في التخزين المحلي
          _updateRememberMeInStorage();

          // إظهار رسالة إعلامية للمستخدم
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _showSnackBar(
              message: AppLocalizations.of(context).uncheckrememberme,
              icon: Icons.info_outline,
              backgroundColor: Colors.blue,
            );
          });
        }
      });
    } catch (e) {
      setState(() {
        _biometricEnabled = false;
      });
    }
  }

  /// تحديث حالة "تذكر البيانات" في التخزين المحلي
  Future<void> _updateRememberMeInStorage() async {
    try {
      // الحصول على البيانات المحفوظة الحالية
      final savedCredentials = await StorageService.getLoginCredentials();

      if (savedCredentials.isNotEmpty) {
        // تحديث البيانات مع إلغاء "تذكر البيانات"
        await StorageService.saveLoginCredentials(
          email: savedCredentials['email'] ?? '',
          password: savedCredentials['password'] ?? '',
          rememberMe: false, // إلغاء التذكر
          serverUrl:
              savedCredentials['serverUrl'] ?? AppConfig.defaultServerUrl,
          database: savedCredentials['database'] ?? AppConfig.defaultDatabase,
        );
      }
    } catch (e) {}
  }

  /// إزالة التركيز من جميع الحقول
  void _clearFocus() {
    _emailFocusNode.unfocus();
    _passwordFocusNode.unfocus();
  }

  /// دالة مساعدة لإنشاء SnackBar موحد
  void _showSnackBar({
    required String message,
    required IconData icon,
    required Color backgroundColor,
    int durationSeconds = 3,
  }) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(icon, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(
              child: Text(message, style: const TextStyle(color: Colors.white)),
            ),
          ],
        ),
        backgroundColor: backgroundColor,
        duration: Duration(seconds: durationSeconds),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  /// دالة مساعدة لإنشاء تصميم الحقول الموحد
  InputDecoration _buildFieldDecoration({
    required String hintText,
    required IconData prefixIcon,
    Widget? suffixIcon,
  }) {
    return InputDecoration(
      hintText: hintText,
      hintStyle: TextStyle(
        color: AppConfig.secondaryTextColorObj.withValues(alpha: 0.7),
        fontSize: 16,
      ),
      prefixIcon: Container(
        margin: const EdgeInsets.all(12),
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: AppConfig.primaryColorObj.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Icon(prefixIcon, color: AppConfig.primaryColorObj, size: 20),
      ),
      suffixIcon: suffixIcon,
      filled: true,
      fillColor: Colors.white,
      border: _buildInputBorder(),
      enabledBorder: _buildInputBorder(),
      focusedBorder: _buildInputBorder(focused: true),
      errorBorder: _buildInputBorder(error: true),
      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
    );
  }

  /// دالة مساعدة لإنشاء حدود الحقول
  OutlineInputBorder _buildInputBorder({
    bool focused = false,
    bool error = false,
  }) {
    Color borderColor;
    double borderWidth;

    if (error) {
      borderColor = AppConfig.errorColorObj;
      borderWidth = 2;
    } else if (focused) {
      borderColor = AppConfig.primaryColorObj;
      borderWidth = 2;
    } else {
      borderColor = AppConfig.dividerColorObj;
      borderWidth = 1;
    }

    return OutlineInputBorder(
      borderRadius: BorderRadius.circular(16),
      borderSide: BorderSide(color: borderColor, width: borderWidth),
    );
  }

  /// دالة مساعدة لإنشاء تأثير الظل للحقول
  List<BoxShadow> _buildFieldShadow(double animationValue) {
    return [
      BoxShadow(
        color: AppConfig.primaryColorObj.withValues(
          alpha: 0.1 * animationValue,
        ),
        blurRadius: 10 * animationValue,
        offset: Offset(0, 5 * animationValue),
      ),
    ];
  }

  /// دالة مساعدة لإدارة حالة التحميل والأخطاء
  void _setLoadingState(bool isLoading, [String? errorMessage]) {
    if (mounted) {
      setState(() {
        _isLoading = isLoading;
        // إذا كان التحميل بدأ، امسح رسالة الخطأ السابقة
        _errorMessage = isLoading ? null : errorMessage;
      });
    }
  }

  /// دالة مساعدة للانتقال إلى شاشة الموظف
  void _navigateToEmployeeScreen(
    OdooService odooService,
    int userId,
    String password,
  ) {
    if (mounted) {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => EmployeeScreen(
            odooService: odooService,
            uid: userId,
            password: password,
          ),
        ),
      );
    }
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
          CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
        );

    _fadeController.forward();
    _slideController.forward();
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _emailFocusNode.dispose();
    _passwordFocusNode.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  /// تسجيل الدخول باستخدام البصمة
  Future<void> _loginWithBiometric() async {
    // إزالة التركيز من الحقول
    _clearFocus();

    _setLoadingState(true);

    try {
      // تتبع محاولة تسجيل الدخول بالبصمة
      await FirebaseAnalyticsService.logEvent(
        name: 'login_attempt',
        parameters: {'method': 'biometric'},
      );

      final result = await BiometricService.loginWithBiometric();

      if (result.success) {
        // تتبع نجاح المصادقة البيومترية
        await FirebaseAnalyticsService.logBiometricAuth(
          action: 'authenticate',
          success: true,
        );
        // إنشاء خدمة Odoo
        final odooService = OdooService(
          baseUrl: result.serverUrl ?? AppConfig.defaultServerUrl,
          database: result.database ?? AppConfig.defaultDatabase,
        );

        // محاولة المصادقة مع Odoo
        final authResult = await odooService.authenticate(
          result.email!,
          result.password!,
        );

        if (authResult.isSuccess) {
          // تتبع نجاح تسجيل الدخول بالبصمة
          await FirebaseAnalyticsService.logLogin(
            method: 'biometric',
            success: true,
          );

          // تعيين معرف المستخدم في Crashlytics
          await FirebaseCrashlyticsService.setUserId(
            authResult.userId.toString(),
          );
          await FirebaseCrashlyticsService.setCustomKeys({
            'login_method': 'biometric',
            'login_timestamp': DateTime.now().toIso8601String(),
          });

          // نجحت المصادقة - الانتقال إلى شاشة الموظف
          _navigateToEmployeeScreen(
            odooService,
            authResult.userId!,
            result.password!,
          );
        } else {
          // تتبع فشل تسجيل الدخول بالبصمة
          await FirebaseAnalyticsService.logLogin(
            method: 'biometric',
            success: false,
            errorCode: authResult.error?.errorCode ?? 'auth_failed',
          );

          _setLoadingState(
            false,
            authResult.error?.userMessage ??
                'فشل في تسجيل الدخول. قد تكون البيانات المحفوظة غير صحيحة أو الخادم غير متاح.',
          );
        }
      } else {
        // تتبع فشل المصادقة البيومترية
        await FirebaseAnalyticsService.logBiometricAuth(
          action: 'authenticate',
          success: false,
          errorCode: result.errorMessage ?? 'biometric_failed',
        );

        _setLoadingState(
          false,
          result.errorMessage ?? 'فشل في المصادقة البيومترية',
        );
      }
    } catch (e) {
      // معالجة الخطأ باستخدام ErrorHandlerService مع تسجيل تلقائي
      final appError = ErrorHandlerService.handleError(
        e,
        context: 'biometric_login',
      );
      _setLoadingState(false, appError.userMessage);
    } finally {
      // التأكد من إيقاف التحميل فقط إذا لم يكن هناك خطأ
      if (mounted && _isLoading) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// دالة تسجيل الدخول
  Future<void> _login() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // إزالة التركيز من الحقول
    _clearFocus();

    _setLoadingState(true);

    try {
      // تتبع محاولة تسجيل الدخول
      await FirebaseAnalyticsService.logEvent(
        name: 'login_attempt',
        parameters: {
          'method': 'email_password',
          'remember_me': _rememberMe ? 'true' : 'false',
        },
      );

      // إنشاء خدمة Odoo باستخدام القيم الافتراضية
      final odooService = OdooService(
        baseUrl: AppConfig.defaultServerUrl,
        database: AppConfig.defaultDatabase,
      );

      // محاولة المصادقة مع تتبع الأداء
      final authResult = await PerformanceMonitoringService.traceHttpRequest(
        url: AppConfig.defaultServerUrl,
        method: 'POST',
        request: () => odooService.authenticate(
          _emailController.text.trim(),
          _passwordController.text.trim(),
        ),
      );

      if (authResult.isSuccess) {
        // تتبع نجاح تسجيل الدخول
        await FirebaseAnalyticsService.logLogin(
          method: 'email_password',
          success: true,
        );

        // تعيين معرف المستخدم في Crashlytics
        await FirebaseCrashlyticsService.setUserId(
          authResult.userId.toString(),
        );
        await FirebaseCrashlyticsService.setCustomKeys({
          'login_method': 'email_password',
          'remember_me': _rememberMe.toString(),
          'login_timestamp': DateTime.now().toIso8601String(),
        });

        // إيقاف البصمة عند تسجيل الدخول بالطريقة العادية
        if (_biometricEnabled) {
          await BiometricService.disableBiometricAuth();
          setState(() {
            _biometricEnabled = false;
          });

          // إظهار رسالة إعلامية للمستخدم
          _showSnackBar(
            message: 'تم إيقاف تسجيل الدخول بالبصمة تلقائياً',
            icon: Icons.fingerprint_outlined,
            backgroundColor: Colors.orange,
          );
        }

        // إذا كانت البصمة مفعلة، إلغاء تفعيل "تذكر بيانات تسجيل الدخول"
        bool finalRememberMe = _rememberMe;
        if (_biometricEnabled && _rememberMe) {
          finalRememberMe = false;
          setState(() {
            _rememberMe = false;
          });

          // إظهار رسالة إعلامية للمستخدم
          _showSnackBar(
            message: AppLocalizations.of(context).uncheckrememberme,
            icon: Icons.info_outline,
            backgroundColor: Colors.blue,
          );
        }

        // حفظ بيانات تسجيل الدخول إذا اختار المستخدم ذلك
        await StorageService.saveLoginCredentials(
          email: _emailController.text.trim(),
          password: _passwordController.text.trim(),
          rememberMe: finalRememberMe,
          serverUrl: AppConfig.defaultServerUrl,
          database: AppConfig.defaultDatabase,
        );

        // نجحت المصادقة - الانتقال إلى شاشة الموظف
        _navigateToEmployeeScreen(
          odooService,
          authResult.userId!,
          _passwordController.text.trim(),
        );
      } else {
        // تتبع فشل تسجيل الدخول
        await FirebaseAnalyticsService.logLogin(
          method: 'email_password',
          success: false,
          errorCode: authResult.error?.errorCode ?? 'unknown_error',
        );

        // فشلت المصادقة
        _setLoadingState(
          false,
          authResult.error?.userMessage ??
              'فشل في تسجيل الدخول. تحقق من البريد الإلكتروني وكلمة المرور أو حالة الاتصال بالخادم.',
        );
      }
    } catch (e) {
      // تتبع خطأ تسجيل الدخول
      await FirebaseAnalyticsService.logLogin(
        method: 'email_password',
        success: false,
        errorCode: 'exception_${e.runtimeType}',
      );

      // تتبع الخطأ في Analytics
      await FirebaseAnalyticsService.logError(
        errorType: 'login_exception',
        errorMessage: e.toString(),
        screenName: 'login_screen',
        functionName: '_login',
      );

      // معالجة الخطأ باستخدام ErrorHandlerService مع تسجيل تلقائي
      final appError = ErrorHandlerService.handleError(
        e,
        context: 'email_password_login',
      );
      _setLoadingState(false, appError.userMessage);
    } finally {
      // التأكد من إيقاف التحميل فقط إذا لم يكن هناك خطأ
      if (mounted && _isLoading) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white,
              const Color(0xFFF8FAFC),
              const Color(0xFFE2E8F0),
              AppConfig.primaryColorObj.withValues(alpha: 0.1),
            ],
            stops: const [0.0, 0.3, 0.7, 1.0],
          ),
        ),
        child: SafeArea(
          child: GestureDetector(
            onTap: _clearFocus,
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: SlideTransition(
                position: _slideAnimation,
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    return SingleChildScrollView(
                      physics: const BouncingScrollPhysics(),
                      child: ConstrainedBox(
                        constraints: BoxConstraints(
                          minHeight: constraints.maxHeight,
                        ),
                        child: IntrinsicHeight(
                          child: Padding(
                            padding: EdgeInsets.symmetric(
                              horizontal: isTablet ? screenWidth * 0.2 : 24,
                              vertical: 8,
                            ),
                            child: Column(
                              children: [
                                SizedBox(height: screenHeight * 0.01),
                                _buildAnimatedHeader(),
                                SizedBox(height: screenHeight * 0.04),
                                _buildInteractiveLoginForm(),
                                SizedBox(height: screenHeight * 0.03),
                                _buildBottomImage(),
                                const Spacer(),
                                _buildFooter(),
                                SizedBox(height: 16),
                              ],
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// بناء الصورة في الأسفل
  Widget _buildBottomImage() {
    return Center(
      child: Image.asset(
        'assets/images/app_logo_down.png',
        width: 200,
        height: 150,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: 200,
            height: 100,
            decoration: BoxDecoration(
              color: const Color(0xFFF3F4F6),
              borderRadius: BorderRadius.circular(12),
            ),
          );
        },
      ),
    );
  }

  /// بناء رأس الصفحة مع الشعار
  Widget _buildAnimatedHeader() {
    return Column(
      children: [
        // الشعار
        _buildEnhancedLogo(),
        SizedBox(height: AppConfig.spacing),

        // عنوان التطبيق
        _buildAppTitle(),
        SizedBox(height: AppConfig.smallSpacing),
      ],
    );
  }

  /// بناء الشعار الأصلي
  Widget _buildEnhancedLogo() {
    return Center(
      child: Image.asset(
        'assets/images/app_logo.png',
        width: 300,
        height: 200,
        fit: BoxFit.contain,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [AppConfig.primaryColorObj, Color(0xFF2563EB)],
                begin: Alignment.bottomCenter,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: AppConfig.primaryColorObj.withValues(alpha: 0.4),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Icon(
              Icons.account_balance,
              size: 60,
              color: AppConfig.whiteColorObj,
            ),
          );
        },
      ),
    );
  }

  /// بناء عنوان التطبيق
  Widget _buildAppTitle() {
    return Column(
      children: [
        // العنوان الرئيسي
        Text(
          AppLocalizations.of(context).appName,
          style: TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.bold,
            color: AppConfig.primaryColorObj,
            letterSpacing: 1.2,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 8),

        // العنوان الفرعي
        Text(
          AppLocalizations.of(context).appSubtitle,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: AppConfig.darkTextColorObj.withValues(alpha: 0.7),
            letterSpacing: 0.5,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// بناء نموذج تسجيل الدخول
  Widget _buildInteractiveLoginForm() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // حقل البريد الإلكتروني المحسن
          _buildEnhancedEmailField(),
          const SizedBox(height: 20),

          // حقل كلمة المرور المحسن
          _buildEnhancedPasswordField(),
          const SizedBox(height: 16),

          // رسالة الخطأ
          if (_errorMessage != null) _buildErrorMessage(),

          const SizedBox(height: 24),

          // زر تسجيل الدخول
          _buildLoginButton(),

          // أيقونة تسجيل الدخول بالبصمة
          if (_biometricEnabled) ...[
            const SizedBox(height: 16),
            _buildBiometricLoginButton(),
          ],
        ],
      ),
    );
  }

  /// بناء حقل البريد الإلكتروني المحسن
  Widget _buildEnhancedEmailField() {
    return AnimatedBuilder(
      animation: _emailFocusNode,
      builder: (context, child) {
        final isFocused = _emailFocusNode.hasFocus;
        return TweenAnimationBuilder<double>(
          duration: const Duration(milliseconds: 200),
          tween: Tween(begin: 0.0, end: isFocused ? 1.0 : 0.0),
          builder: (context, value, child) {
            return Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                boxShadow: _buildFieldShadow(value),
              ),
              child: TextFormField(
                controller: _emailController,
                focusNode: _emailFocusNode,
                keyboardType: TextInputType.emailAddress,
                textInputAction: TextInputAction.next,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: AppConfig.darkTextColorObj,
                ),
                onFieldSubmitted: (_) {
                  FocusScope.of(context).requestFocus(_passwordFocusNode);
                },
                decoration: _buildFieldDecoration(
                  hintText: AppLocalizations.of(context).enterEmail,
                  prefixIcon: Icons.email_outlined,
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return AppLocalizations.of(context).enterEmail;
                  }
                  if (!value.contains('@')) {
                    return AppLocalizations.of(context).validemail;
                  }
                  return null;
                },
              ),
            );
          },
        );
      },
    );
  }

  /// بناء حقل كلمة المرور المحسن
  Widget _buildEnhancedPasswordField() {
    return Column(
      children: [
        AnimatedBuilder(
          animation: _passwordFocusNode,
          builder: (context, child) {
            final isFocused = _passwordFocusNode.hasFocus;
            return TweenAnimationBuilder<double>(
              duration: const Duration(milliseconds: 200),
              tween: Tween(begin: 0.0, end: isFocused ? 1.0 : 0.0),
              builder: (context, value, child) {
                return Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: _buildFieldShadow(value),
                  ),
                  child: TextFormField(
                    controller: _passwordController,
                    focusNode: _passwordFocusNode,
                    obscureText: _obscurePassword,
                    textInputAction: TextInputAction.done,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: AppConfig.darkTextColorObj,
                    ),
                    onFieldSubmitted: (_) {
                      _passwordFocusNode.unfocus();
                      _login();
                    },
                    decoration: _buildFieldDecoration(
                      hintText: AppLocalizations.of(context).enterPassword,
                      prefixIcon: Icons.lock_outlined,
                      suffixIcon: IconButton(
                        icon: Icon(
                          _obscurePassword
                              ? Icons.visibility_off_outlined
                              : Icons.visibility_outlined,
                          color: AppConfig.secondaryTextColorObj,
                        ),
                        onPressed: () {
                          setState(() {
                            _obscurePassword = !_obscurePassword;
                          });
                        },
                      ),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return AppLocalizations.of(context).enterPassword;
                      }
                      if (value.length < 6) {
                        return AppLocalizations.of(context).correctpassword;
                      }
                      return null;
                    },
                  ),
                );
              },
            );
          },
        ),

        const SizedBox(height: 16),

        // خانة اختيار "تذكرني" محسنة
        _buildRememberMeCheckbox(),
      ],
    );
  }

  /// بناء خانة اختيار "تذكرني"
  Widget _buildRememberMeCheckbox() {
    return Row(
      children: [
        Transform.scale(
          scale: 1.2,
          child: Checkbox(
            value: _rememberMe,
            onChanged: (value) {
              setState(() {
                _rememberMe = value ?? false;
              });
            },
            activeColor: AppConfig.primaryColorObj,
            checkColor: AppConfig.whiteColorObj,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ),
        SizedBox(width: AppConfig.spacing),
        Expanded(
          child: GestureDetector(
            onTap: () {
              setState(() {
                _rememberMe = !_rememberMe;
              });
            },
            child: Text(
              AppLocalizations.of(context).rememberMe,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppConfig.darkTextColorObj,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
        // أيقونة معلومات
        GestureDetector(
          onTap: _showRememberMeInfo,
          child: Container(
            padding: const EdgeInsets.all(4),
            child: Icon(
              Icons.info_outline,
              size: 18,
              color: AppConfig.secondaryTextColorObj,
            ),
          ),
        ),
      ],
    );
  }

  /// عرض معلومات حول ميزة "تذكرني"
  void _showRememberMeInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
        ),
        title: Row(
          children: [
            Icon(Icons.security, color: AppConfig.primaryColorObj),
            SizedBox(width: 8),
            Text(
              AppLocalizations.of(context).securityInformation,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppConfig.primaryColorObj,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        content: Text(
          AppLocalizations.of(context).rememberMeInfo,
          style: TextStyle(fontSize: AppConfig.bodyFontSize, height: 1.5),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(AppLocalizations.of(context).understood),
          ),
        ],
      ),
    );
  }

  /// بناء رسالة الخطأ
  Widget _buildErrorMessage() {
    return Container(
      padding: EdgeInsets.all(AppConfig.spacing),
      decoration: BoxDecoration(
        color: AppConfig.errorColorObj.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppConfig.borderRadius),
        border: Border.all(
          color: AppConfig.errorColorObj.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(Icons.error_outline, color: AppConfig.errorColorObj, size: 20),
          SizedBox(width: AppConfig.smallSpacing),
          Expanded(
            child: Text(
              _errorMessage!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppConfig.errorColorObj,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء زر تسجيل الدخول
  Widget _buildLoginButton() {
    return Container(
      width: double.infinity,
      height: AppConfig.buttonHeight,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppConfig.primaryColorObj, Color(0xFF2563EB)],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        borderRadius: BorderRadius.circular(AppConfig.borderRadius),
        boxShadow: [
          BoxShadow(
            color: AppConfig.primaryColorObj.withValues(alpha: 0.4),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: _isLoading ? null : _login,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          ),
        ),
        child: _isLoading
            ? SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  color: AppConfig.whiteColorObj,
                  strokeWidth: 2.5,
                ),
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.login, color: AppConfig.whiteColorObj, size: 22),
                  SizedBox(width: AppConfig.smallSpacing),
                  Text(
                    AppLocalizations.of(context).login,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: AppConfig.whiteColorObj,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  /// بناء أيقونة تسجيل الدخول بالبصمة
  Widget _buildBiometricLoginButton() {
    return Center(
      child: Container(
        width: 70,
        height: 70,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [AppConfig.successColorObj, Color(0xFF10B981)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: AppConfig.successColorObj.withValues(alpha: 0.4),
              blurRadius: 15,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: Tooltip(
            message: 'تسجيل الدخول بالبصمة',
            child: InkWell(
              onTap: _isLoading ? null : _loginWithBiometric,
              borderRadius: BorderRadius.circular(35),
              child: _isLoading
                  ? Center(
                      child: SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          color: AppConfig.whiteColorObj,
                          strokeWidth: 2.5,
                        ),
                      ),
                    )
                  : Center(
                      child: Icon(
                        Icons.fingerprint,
                        color: AppConfig.whiteColorObj,
                        size: 32,
                      ),
                    ),
            ),
          ),
        ),
      ),
    );
  }

  /// بناء تذييل الصفحة
  Widget _buildFooter() {
    return const Text(
      '©2025 IT Department Head Office LY',
      style: TextStyle(
        color: Color(0xFF6B7280),
        fontSize: 14,
        fontWeight: FontWeight.w500,
      ),
      textAlign: TextAlign.center,
    );
  }
}
